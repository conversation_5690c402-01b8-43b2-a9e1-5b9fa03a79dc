# Road Server

一个基于 Spring Boot 的多模块服务器项目，提供云存储代理、内容剪辑、Notion集成等功能。

## 项目结构

```
roadserver/
├── roadserver-app/          # 主应用模块
├── s3-proxy/               # 云存储代理模块
├── road-common/            # 公共模块
├── road-clipper/           # 内容剪辑模块
├── output-notion/          # Notion输出模块
├── output-markdown/        # Markdown输出模块
├── cloud-function/         # 云函数模块
└── pom.xml                 # 主POM文件
```

## 模块说明

### roadserver-app
主应用模块，包含完整的业务逻辑和Web接口，提供：
- 用户管理
- 订单管理
- 微信集成
- 内容管理
- 配置管理

### s3-proxy
云存储代理模块，提供统一的云存储操作接口，支持：
- AWS S3
- MinIO
- 阿里云OSS
- 腾讯云COS
- Cloudflare R2
- Cloudinary

### road-common
公共模块，包含：
- 通用常量定义
- 枚举类型
- 异常处理
- 工具类
- 基础模型

### road-clipper
内容剪辑模块，支持：
- HTML内容解析
- 微信文章处理
- 图片处理与上传
- Notion格式输出
- Markdown格式输出

### output-notion
Notion集成模块，提供：
- Notion API客户端
- 页面创建与管理
- 数据库操作
- 内容同步

### output-markdown
Markdown输出模块，支持：
- Markdown文件生成
- 图片资源处理
- S3存储集成
- 本地文件存储

### cloud-function
云函数模块，提供：
- 无服务器计算支持
- Puppeteer集成
- 动态内容处理

## 快速开始

### 环境要求
- Java 17+
- Maven 3.6+
- MySQL 8.0+
- Redis（可选）

### 构建项目

```bash
# 克隆项目
git clone [repository-url]
cd roadserver

# 构建所有模块
mvn clean package -DskipTests

# 构建特定模块
mvn clean package -pl roadserver-app -am
```

### 运行应用

```bash
# 开发环境
java -jar roadserver-app/target/roadserver-app.jar --spring.profiles.active=dev

# 生产环境
java -jar roadserver-app/target/roadserver-app.jar --spring.profiles.active=prod
```

### Docker 部署

```bash
# 构建镜像
docker build -t roadserver .

# 运行容器
docker run -d -p 8080:8080 --name roadserver roadserver
```

## 配置说明

### 数据库配置
在 `application.yml` 中配置数据库连接：

```yaml
spring:
  datasource:
    url: **********************************************************************
    username: your_username
    password: your_password
```

### 云存储配置
在 `application.yml` 中配置S3：

```yaml
s3:
  providers:
    minio:
      endpoint: http://localhost:9000
      access-key: minioadmin
      secret-key: minioadmin
      bucket: roadserver
```

### 微信配置
在 `application.yml` 中配置微信：

```yaml
wechat:
  cp:
    corp-id: your_corp_id
    secret: your_secret
    token: your_token
    aes-key: your_aes_key
```

## API 文档

### 内容剪辑接口
- POST `/api/clipper/clip` - 剪辑网页内容
- POST `/api/clipper/clipWechat` - 剪辑微信文章

### 云存储接口
- POST `/api/s3/upload` - 文件上传
- GET `/api/s3/download/{fileName}` - 文件下载
- DELETE `/api/s3/delete/{fileName}` - 文件删除

### Notion接口
- POST `/api/notion/page` - 创建Notion页面
- GET `/api/notion/pages` - 获取页面列表

## 使用示例

### 内容剪辑

```java
// 剪辑网页内容
@PostMapping("/clip")
public Result<String> clipContent(@RequestBody ClipRequest request) {
    return clipperService.clipContent(request.getUrl(), request.getOptions());
}
```

### 文件上传到S3

```java
// 创建S3配置
S3Config config = S3Config.builder()
    .provider("minio")
    .endpoint("http://minio.example.com")
    .accessKey("accessKey")
    .secretKey("secretKey")
    .bucket("images")
    .build();

// 使用S3服务
@Autowired
private S3ProxyService s3ProxyService;

// 上传文件
File file = new File("/path/to/file.jpg");
UploadResult result = s3ProxyService.uploadFile(config, file, null);

if (result.isSuccess()) {
    System.out.println("上传成功: " + result.getUrl());
} else {
    System.out.println("上传失败: " + result.getErrorMessage());
}

// 下载网络资源并上传
String imageUrl = "https://example.com/image.jpg";
UploadResult result = s3ProxyService.downloadAndUpload(config, imageUrl, null);
```

### Notion页面创建

```java
// 创建Notion页面
PageCreateRQ request = PageCreateRQ.builder()
    .parent(new Parent("database_id"))
    .properties(new Properties()
        .addProperty("title", new Title("页面标题"))
        .addProperty("内容", new RichText("页面内容")))
    .build();

PageCreateRS response = notionClient.createPage(request);
```

## 开发指南

### 代码规范
- 遵循阿里巴巴Java开发规范
- 使用Lombok简化代码
- 统一异常处理
- RESTful API设计

### 模块依赖
```
roadserver-app
├── road-common (公共模块)
├── s3-proxy (云存储)
├── road-clipper (内容剪辑)
├── output-notion (Notion输出)
└── output-markdown (Markdown输出)
```

### 测试
```bash
# 运行所有测试
mvn test

# 运行特定模块测试
mvn test -pl roadserver-app

# 跳过测试构建
mvn clean package -DskipTests
```
