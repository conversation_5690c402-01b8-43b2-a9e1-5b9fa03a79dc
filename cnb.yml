master:
  push:
    - docker:
        # 声明构建环境，可以在 dockerhub 上 https://hub.docker.com/_/maven 找到您需要maven和jdk版本
        image: maven:3.9.11-amazoncorretto-21
        volumes:
          # 声明式的构建缓存 https://docs.cnb.cool/grammar/pipeline.html#volumes
          - /root/.m2:copy-on-write
      services:
        # 流水线中启用 docker 服务
        - docker
      stages:
        - name: mvn package
          script: |
            # 合并 ./settings.xml 和 /root/.m2/settings.xml
            mvn clean package -s ./settings.xml
            mvn deploy -s ./settings.xml
        # 云原生构建自动构建Docker镜像并将它发布到制品库，【上传Docker制品】https://docs.cnb.cool/artifact/docker.html
        - name: docker build
          script:
            - docker build -t ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}:${CNB_COMMIT} .
        - name: docker push
          script:
            - docker push ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}:${CNB_COMMIT}