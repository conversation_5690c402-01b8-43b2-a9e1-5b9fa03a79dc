package com.maixi.road.admin.manager;

import static com.maixi.road.common.core.enums.error.ErrorCodeEnum.BIZ_ERROR;

import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import com.maixi.road.admin.biz.domain.CustomConfig;
import com.maixi.road.admin.biz.domain.UserMessage;
import com.maixi.road.admin.biz.service.ICustomConfigService;
import com.maixi.road.admin.biz.service.INotionResourceService;
import com.maixi.road.admin.biz.service.IUserArticleService;
import com.maixi.road.admin.biz.service.IUserMessageService;
import com.maixi.road.clipper.ClipperApi;
import com.maixi.road.common.core.enums.error.ClipperErrCodeEnum;
import com.maixi.road.common.core.exception.ClipperException;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.ClipperResponse;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.core.model.dto.ResolveRS;
import com.maixi.road.common.core.model.dto.TextMessageRequest;
import com.maixi.road.common.core.model.request.MessageSyncParam;
import com.maixi.road.common.core.utils.AssertUtils;
import com.maixi.road.common.core.utils.UrlUtils;
import com.maixi.road.framework.annotation.CountLimit;
import com.maixi.road.framework.config.CacheManager;
import com.maixi.road.framework.config.RedisManager;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ClipperManager {

    @Resource
    private UserManager userManager;
    @Resource
    private ClipperApi clipperApi;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisManager redisManager;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private IUserArticleService userArticleService;
    @Resource
    private IUserMessageService userMessageService;
    @Resource
    private ICustomConfigService customConfigService;
    @Resource
    private INotionResourceService notionResourceService;

    /**
     *
     * 记录 unionId 的解析次数
     *
     * @param url
     * @param unionId
     * @return
     */
    @CountLimit(countType = "resolve_count")
    public ResolveRS resolve(String url, String unionId) {
        ClipperResponse<ResolveRS> response = clipperApi.parseUrl(url, unionId);
        if (!response.isSuccess()) {
            log.error("解析失败, url={}, msg={}", url, response.getMessage());
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, response.getMessage());
        }
        return response.getData();
    }

    /**
     *
     * @param param
     * @param unionId
     * @return
     */
    @CountLimit(countType = "save_count")
    public Boolean submit(ResolveFormRQ param, String unionId) {
        ClipperResponse<Boolean> submitArticle = clipperApi.saveContent(param, unionId);
        if (!submitArticle.isSuccess()) {
            log.error("创建 notion 页面失败, msg={},url={},urlKey={}", submitArticle.getMessage(), param.getLink(),
                    UrlUtils.generateMD5HexString(param.getLink()));
            throw RoadException.create(BIZ_ERROR, submitArticle.getMessage());
        }
        redisManager.articleSumIncrementOne(unionId);
        return true;
    }

    /**
     * 同步消息到剪藏服务
     * 记录 unionId 的保存次数
     *
     * @param param   消息同步参数
     * @param unionId 用户唯一标识
     * @return 同步结果
     */
    @CountLimit(countType = "save_count")
    public String syncMessage(MessageSyncParam param, String unionId) {
        // 先判断用户是保存到 notion 还是 Obsidian，然后再进行校验
        CustomConfig customConfig = customConfigService.getCustomConfigByUnionId(unionId);
        if (customConfig == null || customConfig.getToObsidian() == 0) {
            UserMessage userMessage = userMessageService.getByUnionId(unionId);
            check(userMessage);
        }
        TextMessageRequest request = new TextMessageRequest();
        request.setContent(param.getContent());
        ClipperResponse<String> syncTextMessage = clipperApi.saveText(request, unionId);
        if (!syncTextMessage.isSuccess()) {
            throw RoadException.create(BIZ_ERROR, syncTextMessage.getMessage());
        }
        return syncTextMessage.getData();
    }


    /**
     * 检查用户消息配置
     *
     * @param userMessage 用户消息配置
     */
    private void check(UserMessage userMessage) {
        AssertUtils.notNullWithBizExp(userMessage, "您还未关联消息数据库");
        AssertUtils.notNullWithBizExp(userMessage.getAccessToken(), "您还未关联消息数据库");
        AssertUtils.notNullWithBizExp(userMessage.getDatabaseId(), "您还未关联消息数据库");
    }

}
