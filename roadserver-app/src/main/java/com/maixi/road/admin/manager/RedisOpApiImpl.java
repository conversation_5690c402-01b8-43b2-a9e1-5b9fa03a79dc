package com.maixi.road.admin.manager;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.maixi.road.common.core.model.dto.ResolveRS;
import com.maixi.road.common.core.utils.UrlUtils;
import com.maixi.road.common.service.redis.api.RedisOpApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class RedisOpApiImpl implements RedisOpApi {

    @Resource
    private RedissonClient redissonClient;

    @Override
    public void saveClipResult(String url, ResolveRS resolveRS) {
        if (resolveRS == null || CollectionUtils.isEmpty(resolveRS.getBlocks())) {
            log.warn("保存正文结果为空, url={}", url);
            return;
        }
        String key = UrlUtils.generateMD5HexString(url);
        log.info("保存正文结果, key={}, url={}, blocks.size={}", key, url, resolveRS.getBlocks().size());
        redissonClient.<ResolveRS>getBucket(key).set(resolveRS, Duration.ofMinutes(10));
    }

    @Override
    public Future<ResolveRS> getClipResult(String url) {
        String key = UrlUtils.generateMD5HexString(url);
        log.info("开始从Redis获取正文, key={},url={}", key, url);
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            long timeout = 15000;
            int count = 0;

            while (System.currentTimeMillis() - startTime < timeout) {
                RBucket<ResolveRS> bucket = redissonClient.getBucket(key);
                ResolveRS result = bucket.get();
                if (result != null) {
                    log.info("从Redis获取正文成功, key={}, count={}, blocks.size={}", key, count, result.getBlocks().size());
                    return result;
                }
                log.info("从Redis获取正文结果为空, key={}, count={}", key, count);
                count++;

                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Interrupted while waiting for clip result", e);
                    break;
                }
            }

            log.info("从Redis获取正文超时, key={},url={}", key, url);
            return null;
        });
    }

}
