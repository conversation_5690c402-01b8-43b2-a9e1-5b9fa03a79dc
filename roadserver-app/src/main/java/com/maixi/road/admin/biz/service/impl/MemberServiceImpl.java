package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.MemberMapper;
import com.maixi.road.admin.biz.domain.Member;
import com.maixi.road.admin.biz.domain.VipOpenList;
import com.maixi.road.admin.biz.service.IMemberService;
import com.maixi.road.admin.biz.service.INotionResourceService;
import com.maixi.road.admin.biz.service.IVipOpenListService;
import com.maixi.road.common.business.user.enums.VipTypeEnum;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Service
public class MemberServiceImpl extends ServiceImpl<MemberMapper, Member> implements IMemberService {

    @Resource
    private INotionResourceService notionResourceService;
    @Resource
    private IVipOpenListService vipOpenListService;

    @Override
    public boolean checkPromotionCode(String promotionCode) {
        if ("BetterAndBetter".equals(promotionCode)) {
            return true;
        }
        LambdaQueryWrapper<Member> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Member::getPromotionCode, promotionCode);
        queryWrapper.gt(Member::getEndTime, LocalDateTime.now());
        queryWrapper.eq(Member::getDeleted, 0);
        queryWrapper.orderByDesc(Member::getId);
        queryWrapper.last("LIMIT 1");
        Optional<Member> oneOpt = getOneOpt(queryWrapper);
        return oneOpt.isPresent();
    }

    @Override
    public Integer remainForeverVipCount() {
        LambdaQueryWrapper<Member> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Member::getVipType, VipTypeEnum.FOREVER_VIP.getType());
        queryWrapper.eq(Member::getType, 1);
        queryWrapper.eq(Member::getDeleted, 0);
        long count = this.count(queryWrapper);

        Integer foreverVipLimitCount = notionResourceService.getForeverVipLimitCount();
        return (int) (foreverVipLimitCount - count);
    }

    @Override
    public boolean vipOpen(String unionId) {
        Integer vipOpen = notionResourceService.getVipOpen();
        if (vipOpen == null || vipOpen == 0) {
            return false;
        } else if (vipOpen == 1) {
            return true;
        } else if (vipOpen == 2) {
            List<VipOpenList> list = vipOpenListService.list();
            List<String> vipOpenUnionIdList = list.stream().map(VipOpenList::getUnionId).toList();
            return vipOpenUnionIdList.contains(unionId);
        }
        return false;
    }

    @Override
    public Integer getVipType(String unionId) {
        LambdaQueryWrapper<Member> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Member::getUnionId, unionId);
        queryWrapper.eq(Member::getDeleted, 0);
        queryWrapper.orderByDesc(Member::getId);
        queryWrapper.last("LIMIT 1");
        Optional<Member> oneOpt = getOneOpt(queryWrapper);
        if (oneOpt.isPresent()) {
            return oneOpt.get().getVipType();
        }
        return -1;
    }

    @Override
    public Member selectByUnionId(String unionId) {
        LambdaQueryWrapper<Member> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Member::getUnionId, unionId);
        queryWrapper.eq(Member::getDeleted, 0);
        queryWrapper.orderByDesc(Member::getId);
        queryWrapper.last("LIMIT 1");
        Optional<Member> oneOpt = getOneOpt(queryWrapper);
        return oneOpt.orElse(null);
    }

    @Override
    public Member selectByUnionIdAndType(String unionId, Integer type) {
        LambdaQueryWrapper<Member> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Member::getUnionId, unionId);
        queryWrapper.eq(Member::getDeleted, 0);
        queryWrapper.eq(Member::getType, type);
        queryWrapper.orderByDesc(Member::getId);
        queryWrapper.last("LIMIT 1");
        Optional<Member> oneOpt = getOneOpt(queryWrapper);
        return oneOpt.orElse(null);
    }

    @Override
    public Member getByDiscountCode(String discountCode) {
        LambdaQueryWrapper<Member> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Member::getPromotionCode, discountCode);
        queryWrapper.eq(Member::getDeleted, 0);
        queryWrapper.gt(Member::getEndTime, LocalDateTime.now());
        queryWrapper.orderByDesc(Member::getId);
        queryWrapper.last("LIMIT 1");
        Optional<Member> oneOpt = getOneOpt(queryWrapper);
        return oneOpt.orElse(null);
    }

    @Override
    public boolean checkIfVip(String unionId) {
        LambdaQueryWrapper<Member> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Member::getUnionId, unionId);
        queryWrapper.eq(Member::getDeleted, 0);
        queryWrapper.gt(Member::getEndTime, LocalDateTime.now());
        queryWrapper.orderByDesc(Member::getId);
        queryWrapper.last("LIMIT 1");
        Optional<Member> oneOpt = getOneOpt(queryWrapper);
        return oneOpt.isPresent();
    }
}
