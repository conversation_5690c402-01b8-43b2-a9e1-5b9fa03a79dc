package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("custom_config")
public class CustomConfig {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("union_id")
    private String unionId;

    /**
     * 始终使用自有图床
     */
    @TableField("always_use_pic_cloud")
    private Integer alwaysUsePicCloud;

    /**
     * 始终优先使用 cloudinary 图床
     */
    @TableField("always_use_cloudinary")
    private Integer alwaysUseCloudinary;

    /**
     * 支持通用网页
     */
    @TableField("support_all_site")
    private Integer supportAllSite;

    /**
     * 不要 icon
     */
    @TableField("no_icon")
    private Integer noIcon;

    /**
     * 不要 cover
     */
    @TableField("no_cover")
    private Integer noCover;

    /**
     * 是否将快捷剪藏页作为首页
     */
    @TableField("quickclip_as_enterpage")
    private Integer quickClipAsEnterPage;

    /**
     * 是否保存链接文本，默认为否
     */
    @TableField("save_link_text")
    private Integer saveLinkText;

    /**
     * 是否保存到 obsidian
     */
    @TableField("to_obsidian")
    private Integer toObsidian;

    @TableField("gmt_create")
    private LocalDateTime gmtCreate;

    @TableField("gmt_update")
    private LocalDateTime gmtUpdate;

    @TableField("deleted")
    private Integer deleted;
}
