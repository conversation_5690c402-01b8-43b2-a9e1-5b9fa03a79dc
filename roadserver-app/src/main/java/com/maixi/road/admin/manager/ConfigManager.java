package com.maixi.road.admin.manager;

import com.alibaba.fastjson.JSON;
import com.maixi.road.admin.biz.domain.CloudinaryConfig;
import com.maixi.road.admin.biz.domain.CustomConfig;
import com.maixi.road.admin.biz.domain.OssConfig;
import com.maixi.road.admin.biz.service.ICloudinaryConfigService;
import com.maixi.road.admin.biz.service.ICustomConfigService;
import com.maixi.road.admin.biz.service.IOssConfigService;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.enums.error.ImageCloudErrCodeEnum;
import com.maixi.road.common.core.enums.error.ParamErrCodeEnum;
import com.maixi.road.common.core.exception.ImageCloudException;
import com.maixi.road.common.core.exception.ParamException;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.request.CloudinaryCreateRequest;
import com.maixi.road.common.core.model.request.CustomConfigCreateReq;
import com.maixi.road.common.core.model.request.OssConfigCreateReq;
import com.maixi.road.common.core.model.response.CloudinaryConfigBO;
import com.maixi.road.common.core.model.response.CloudinaryConfigVo;
import com.maixi.road.common.core.model.response.CustomConfigVo;
import com.maixi.road.common.core.model.response.OssConfigVo;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

@Slf4j
@Service
public class ConfigManager {

    @Resource
    private ICloudinaryConfigService cloudinaryConfigService;
    @Resource
    private ICustomConfigService customConfigService;
    @Resource
    private IOssConfigService ossConfigService;
    @Resource
    private S3Manager s3Manager;

    public CloudinaryConfigVo getCloudinaryConfig(String unionId) {
        CloudinaryConfig cloudinaryConfig = cloudinaryConfigService.getCloudinaryConfigByUnionId(unionId);
        CloudinaryConfigBO configBO = new CloudinaryConfigBO();
        BeanUtils.copyProperties(cloudinaryConfig, configBO);
        return new CloudinaryConfigVo(configBO);
    }

    public CloudinaryConfig getCloudinaryConfigV2(String unionId) {
        return cloudinaryConfigService.getCloudinaryConfigByUnionId(unionId);
    }

    public CustomConfigVo getCustomConfig(String unionId) {
        Optional<CustomConfig> configOpt = customConfigService.getCustomConfigOptByUnionId(unionId);
        if (configOpt.isPresent()) {
            CustomConfig customConfig = configOpt.get();
            return CustomConfigVo.builder()
                    .alwaysUseCloudinary(customConfig.getAlwaysUseCloudinary() == 1)
                    .alwaysUsePicCloud(customConfig.getAlwaysUsePicCloud() == 1)
                    .noCover(customConfig.getNoCover() == 1)
                    .noIcon(customConfig.getNoIcon() == 1)
                    .supportAllSite(customConfig.getSupportAllSite() == 1)
                    .quickClipAsEnterPage(customConfig.getQuickClipAsEnterPage() == 1)
                    .saveLinkText(customConfig.getSaveLinkText() == 1)
                    .toObsidian(customConfig.getToObsidian() == 1)
                    .build();
        }
        return CustomConfigVo.builder()
                .alwaysUseCloudinary(true)
                .alwaysUsePicCloud(true)
                .noCover(false)
                .noIcon(false)
                .supportAllSite(false)
                .quickClipAsEnterPage(false)
                .saveLinkText(false)
                .toObsidian(false)
                .build();
    }

    public void postCustomConfig(String unionId, CustomConfigCreateReq createReq) {
        // 如果设置开启通用剪藏，需判断是否配置了图床
        boolean hasImgClient = s3Manager.hasImgClient(unionId);
        if (createReq.getSupportAllSite() && !hasImgClient) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "通用剪藏开启需要先配置图床");
        }
        Optional<CustomConfig> configOpt = customConfigService.getCustomConfigOptByUnionId(unionId);
        if (configOpt.isPresent()) {
            CustomConfig customConfig = configOpt.get();
            customConfig.setAlwaysUsePicCloud(createReq.getAlwaysUsePicCloud() ? 1 : 0);
            customConfig.setAlwaysUseCloudinary(createReq.getAlwaysUseCloudinary() ? 1 : 0);
            customConfig.setSupportAllSite(createReq.getSupportAllSite() ? 1 : 0);
            customConfig.setNoCover(createReq.getNoCover() ? 1 : 0);
            customConfig.setNoIcon(createReq.getNoIcon() ? 1 : 0);
            customConfig.setGmtUpdate(LocalDateTime.now());
            customConfig.setQuickClipAsEnterPage(Optional.ofNullable(createReq.getQuickClipAsEnterPage()).orElse(false) ? 1 : 0);
            customConfig.setSaveLinkText(Optional.ofNullable(createReq.getSaveLinkText()).orElse(false) ? 1 : 0);
            customConfig.setToObsidian(Optional.ofNullable(createReq.getToObsidian()).orElse(false) ? 1 : 0);
            customConfigService.updateById(customConfig);
        } else {
            CustomConfig customConfig = new CustomConfig();
            customConfig.setUnionId(unionId);
            customConfig.setAlwaysUsePicCloud(createReq.getAlwaysUsePicCloud() ? 1 : 0);
            customConfig.setAlwaysUseCloudinary(createReq.getAlwaysUseCloudinary() ? 1 : 0);
            customConfig.setSupportAllSite(createReq.getSupportAllSite() ? 1 : 0);
            customConfig.setNoCover(Optional.ofNullable(createReq.getNoCover()).orElse(false) ? 1 : 0);
            customConfig.setNoIcon(Optional.ofNullable(createReq.getNoIcon()).orElse(false) ? 1 : 0);
            customConfig.setQuickClipAsEnterPage(Optional.ofNullable(createReq.getQuickClipAsEnterPage()).orElse(false) ? 1 : 0);
            customConfig.setToObsidian(Optional.ofNullable(createReq.getToObsidian()).orElse(false) ? 1 : 0);
            customConfig.setSaveLinkText(Optional.ofNullable(createReq.getSaveLinkText()).orElse(false) ? 1 : 0);
            customConfig.setGmtCreate(LocalDateTime.now());
            customConfig.setGmtUpdate(LocalDateTime.now());
            customConfigService.save(customConfig);
        }
    }

    /**
     *
     * @param unionId
     * @param createReq
     */
    public void postOssConfig(String unionId, OssConfigCreateReq createReq) {
        Optional<OssConfig> configOpt = ossConfigService.getOssConfigOptByUnionId(unionId);
        if (configOpt.isPresent()) {
            OssConfig ossConfig = configOpt.get();
            ossConfig.setType(createReq.getType());
            ossConfig.setAccessKey(createReq.getAccessKey().trim());
            // 这里还不能直接加密，要验证过后再加密
            ossConfig.setAccessSecret(createReq.getAccessSecret().trim());
            ossConfig.setBucketName(createReq.getBucketName().trim());


            ossConfig.setCustomDomain(Optional.ofNullable(createReq.getCustomDomain()).map(String::trim).orElse(null));
            if ("r2".equals(createReq.getType())) {
                if (StringUtils.isBlank(createReq.getRegion())) {
                    throw ParamException.create(ParamErrCodeEnum.DEFAULT_ERROR, "R2的 accountId 必填");
                }
                // R2的 region 其实是 account_id
                ossConfig.setRegion(createReq.getRegion().trim());
                ossConfig.setEndpoint("https://" + createReq.getRegion() + ".r2.cloudflarestorage.com");
            } else {
                ossConfig.setRegion(Optional.ofNullable(createReq.getRegion()).map(String::trim).orElse(null));
                ossConfig.setEndpoint(createReq.getEndpoint().trim());
            }


            ossConfig.setGmtUpdate(System.currentTimeMillis());
            boolean verify = s3Manager.verify(ossConfig);
            if (!verify) {
                log.info("postOssConfig verify result={}, config={}", verify, JSON.toJSONString(ossConfig));
                throw ImageCloudException.create(ImageCloudErrCodeEnum.VERIFY_FAIL);
            }
            // 这里重写了 updateById 方法，会自动加密
            ossConfigService.updateById(ossConfig);
        } else {
            OssConfig ossConfig = new OssConfig();
            ossConfig.setUnionId(unionId);
            ossConfig.setType(createReq.getType());
            ossConfig.setAccessKey(createReq.getAccessKey().trim());
            // 这里还不能直接加密，要验证过后再加密
            ossConfig.setAccessSecret(createReq.getAccessSecret().trim());
            ossConfig.setBucketName(createReq.getBucketName().trim());

            ossConfig.setCustomDomain(Optional.ofNullable(createReq.getCustomDomain()).map(String::trim).orElse(null));
            if ("r2".equals(createReq.getType())) {
                if (StringUtils.isBlank(createReq.getRegion())) {
                    throw ParamException.create(ParamErrCodeEnum.DEFAULT_ERROR, "R2的 accountId 必填");
                }
                // R2的 region 其实是 account_id
                ossConfig.setRegion(createReq.getRegion().trim());
                ossConfig.setEndpoint("https://" + createReq.getRegion() + ".r2.cloudflarestorage.com");
            } else {
                ossConfig.setRegion(Optional.ofNullable(createReq.getRegion()).map(String::trim).orElse(null));
                ossConfig.setEndpoint(createReq.getEndpoint().trim());
            }

            ossConfig.setGmtCreate(System.currentTimeMillis());
            ossConfig.setGmtUpdate(System.currentTimeMillis());
            boolean verify = s3Manager.verify(ossConfig);
            if (!verify) {
                log.info("postOssConfig verify result={}, config={}", verify, JSON.toJSONString(ossConfig));
                throw ImageCloudException.create(ImageCloudErrCodeEnum.VERIFY_FAIL);
            }
            // 这里重写了 save 方法，会自动加密
            ossConfigService.save(ossConfig);
        }
        // 异步上传默认图片
        // 公众号
        // 小红书
        // 下厨房
        // 少数派
        // 即刻
        // 小宇宙
        // 豆瓣
        // 人人都是产品经理
        // untag
        // 掘金
        // YouTube
        // X
        // 通用网页
        // 文本
        // 图片
        // 视频
        // 音频
        // 文件
        // 聊天记录
    }

    public OssConfigVo getOssConfig(String unionId) {
        Optional<OssConfig> ossConfigOpt = ossConfigService.getOssConfigOptByUnionId(unionId);
        if (ossConfigOpt.isEmpty()) {
            return null;
        }
        OssConfig ossConfig = ossConfigOpt.get();
        return OssConfigVo.builder()
                .type(ossConfig.getType())
                .endpoint(ossConfig.getEndpoint())
                .accessKey(ossConfig.getAccessKey())
                .accessSecret(ossConfig.getAccessSecret())
                .bucketName(ossConfig.getBucketName())
                .customDomain(ossConfig.getCustomDomain())
                .region(ossConfig.getRegion())
                .build();
    }

    public void postCloudinaryConfig(CloudinaryCreateRequest param, String unionId) {
        CloudinaryConfig config = cloudinaryConfigService.getCloudinaryConfigByUnionId(unionId);
        if (config == null) {
            config = new CloudinaryConfig();
            buildCloudinaryConfig(config, param, unionId);
            // 这里重写了 save 方法，会自动加密
            cloudinaryConfigService.save(config);
        } else {
            buildCloudinaryConfig(config, param, unionId);
            // 这里重写了 updateById 方法，会自动加密
            cloudinaryConfigService.updateById(config);
        }
    }

    private void buildCloudinaryConfig(CloudinaryConfig config, CloudinaryCreateRequest param, String unionId) {
        config.setCloudName(param.getCloudName());
        config.setUnionId(unionId);
        config.setApiKey(param.getApiKey());
        // 这里还不能直接加密，要验证过后再加密
        config.setApiSecret(param.getApiSecret());
        boolean verify = s3Manager.verify(config);
        if (!verify) {
            log.info("postCloudinaryConfig verify result={}, config={}", verify, JSON.toJSONString(config));
            throw ImageCloudException.create(ImageCloudErrCodeEnum.VERIFY_FAIL);
        }
    }

    public void removeCloudinaryConfig(String mainUnionId) {
        cloudinaryConfigService.deleteByUnionId(mainUnionId);
    }

    public void removeOssConfig(String mainUnionId) {
        ossConfigService.deleteByUnionId(mainUnionId);
    }
}
