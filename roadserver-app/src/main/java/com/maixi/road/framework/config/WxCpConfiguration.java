package com.maixi.road.framework.config;

import java.util.Map;
import java.util.stream.Collectors;

import com.maixi.road.wechat.handler.CpAuditApprovedHandler;
import com.maixi.road.wechat.handler.CpAuditNotifyHandler;
import lombok.Getter;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.google.common.collect.Maps;
import com.maixi.road.common.core.constant.CommonConstants;
import com.maixi.road.framework.config.WxCpProperties.AppConfig;
import com.maixi.road.wechat.handler.CpLogHandler;
import com.tencent.wework.Finance;

import cn.hutool.core.io.FileUtil;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import me.chanjar.weixin.cp.constant.WxCpConsts;
import me.chanjar.weixin.cp.message.WxCpMessageRouter;

/**
 * 单实例配置
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(WxCpProperties.class)
public class WxCpConfiguration {

    @Resource
    private CpLogHandler cpLogHandler;
    @Resource
    private CpAuditNotifyHandler cpAuditNotifyHandler;
    @Resource
    private CpAuditApprovedHandler cpAuditApprovedHandler;
    @Resource
    private WxCpProperties properties;

    @Getter
    private static Map<Integer, WxCpMessageRouter> routers = Maps.newHashMap();
    private static Map<Integer, WxCpService> cpServices = Maps.newHashMap();

    public static WxCpService getCpService(Integer agentId) {
        return cpServices.get(agentId);
    }

    @PostConstruct
    public void initServices() {
        log.info("注册企业微信服务");
        cpServices = this.properties.getAppConfigs()
                .stream()
                .map(appConfig -> {
                    // 获取配置
                    WxCpDefaultConfigImpl configStorage = getConfigStorage(appConfig);
                    // 创建服务
                    WxCpServiceImpl service = new WxCpServiceImpl();
                    service.setWxCpConfigStorage(configStorage);
                    // 创建路由
                    WxCpMessageRouter router = this.newRouter(service);
                    // 注册路由
                    routers.put(appConfig.getAgentId(), router);
                    return service;
                }).collect(Collectors.toMap(service -> service.getWxCpConfigStorage().getAgentId(), a -> a));
    }

    private WxCpDefaultConfigImpl getConfigStorage(AppConfig appConfig) {
        WxCpDefaultConfigImpl configStorage = new WxCpDefaultConfigImpl();
        configStorage.setCorpId(this.properties.getCorpId());
        configStorage.setAgentId(appConfig.getAgentId());
        configStorage.setCorpSecret(appConfig.getSecret());
        configStorage.setToken(appConfig.getToken());
        configStorage.setAesKey(appConfig.getAesKey());
        configStorage.setMsgAuditPriKey(CommonConstants.priKey);
        configStorage.setMsgAuditSecret("WENcyFYJKvrhnnBddA4hYeAjep6G9d5v26DQ7Ekq2XQ");
        if (Finance.isWindows()) {
            configStorage.setMsgAuditLibPath(FileUtil.getAbsolutePath("lib\\WeWorkFinanceSdk.dll"));
        } else {
            configStorage.setMsgAuditLibPath("/home/<USER>/libWeWorkFinanceSdk_Java.so");
            log.info("lib path: " + configStorage.getMsgAuditLibPath());
        }
        return configStorage;
    }

    private WxCpMessageRouter newRouter(WxCpService wxCpService) {
        final WxCpMessageRouter newRouter = new WxCpMessageRouter(wxCpService);

        // 记录所有消息
        newRouter.rule()
                .handler(cpLogHandler)
                .next();

        // 处理产生会话回调事件
        newRouter.rule()
                .msgType(WxConsts.XmlMsgType.EVENT)
                .event(WxCpConsts.EventType.MSGAUDIT_NOTIFY)
                .handler(cpAuditNotifyHandler)
                .end();

        // 处理客户同意聊天内容存档事件
        newRouter.rule()
                .msgType(WxConsts.XmlMsgType.EVENT)
                .event(WxCpConsts.EventType.CHANGE_EXTERNAL_CONTACT)
                .matcher(message -> WxCpConsts.MsgAuditChangeType.MSG_AUDIT_APPROVED.equals(message.getChangeType()))
                .handler(cpAuditApprovedHandler)
                .end();

        return newRouter;
    }

}
