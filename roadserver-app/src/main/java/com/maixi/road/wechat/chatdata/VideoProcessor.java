package com.maixi.road.wechat.chatdata;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.maixi.road.admin.biz.service.IChatOpenListService;
import com.maixi.road.admin.manager.S3Manager;
import com.maixi.road.common.business.wechat.enums.MsgTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.markdown.service.MarkdownOutputService;
import com.maixi.road.notion.remote.NotionClient;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMsgAuditService;

@Slf4j
public class VideoProcessor extends BaseProcessor {

    private final NotionClient notionClient;
    private final S3Manager s3Manager;
    private final WxCpMsgAuditService msgAuditService;
    private final IChatOpenListService chatOpenListService;
    private final MarkdownOutputService markdownOutputService;
    private final ConfigQueryApi configQueryApi;

    public VideoProcessor(NotionClient notionClient, S3Manager s3Manager,
            WxCpMsgAuditService msgAuditService, IChatOpenListService chatOpenListService,
            MarkdownOutputService markdownOutputService, ConfigQueryApi configQueryApi) {
        this.notionClient = notionClient;
        this.s3Manager = s3Manager;
        this.msgAuditService = msgAuditService;
        this.chatOpenListService = chatOpenListService;
        this.markdownOutputService = markdownOutputService;
        this.configQueryApi = configQueryApi;
    }

    @Override
    protected NotionClient getNotionClient() {
        return notionClient;
    }

    @Override
    protected WxCpMsgAuditService getMsgAuditService() {
        return msgAuditService;
    }

    @Override
    protected S3Manager getS3Manager() {
        return s3Manager;
    }

    @Override
    protected IChatOpenListService getChatOpenListService() {
        return chatOpenListService;
    }
    
    @Override
    protected MarkdownOutputService getMarkdownOutputService() {
        return markdownOutputService;
    }
    
    @Override
    protected ConfigQueryApi getConfigQueryApi() {
        return configQueryApi;
    }

    @Override
    public void process(JSONObject message, Long sdk, String unionId, List<String> tagList)
            throws WxErrorException, IOException {

        // 1. 验证用户权限
        if (!isAuthorizedUser(unionId)) {
            return;
        }

        // 2. 检查消息时效性
        boolean isValidTime = isMessageValid(message);

        // 3. 构建Block
        Block block;
        if (isValidTime) {
            block = buildVideoBlock(message, unionId, sdk, false);
        } else {
            block = Block.paragraph(RichTexts.builder()
                    .rich_text(Collections.singletonList(RichText.simpleText("视频消息已过期，无法同步"))).build());
        }
        String title = "视频_"+ LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        // 4. 根据用户配置发送消息到相应平台
        super.output(unionId, title, MsgTypeEnum.VIDEO, Collections.singletonList(block),
                tagList.toArray(new String[0]));
    }

}
