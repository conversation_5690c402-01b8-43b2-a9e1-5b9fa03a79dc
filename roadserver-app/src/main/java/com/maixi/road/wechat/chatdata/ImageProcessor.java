package com.maixi.road.wechat.chatdata;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.maixi.road.admin.biz.service.IChatOpenListService;
import com.maixi.road.admin.manager.S3Manager;
import com.maixi.road.common.business.wechat.enums.MsgTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.markdown.service.MarkdownOutputService;
import com.maixi.road.notion.remote.NotionClient;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMsgAuditService;

@Slf4j
public class ImageProcessor extends BaseProcessor {

    private final NotionClient notionClient;
    private final S3Manager s3Manager;
    private final WxCpMsgAuditService msgAuditService;
    private final MarkdownOutputService markdownOutputService;
    private final ConfigQueryApi configQueryApi;

    public ImageProcessor(NotionClient notionClient, S3Manager s3Manager, WxCpMsgAuditService msgAuditService,
            MarkdownOutputService markdownOutputService, ConfigQueryApi configQueryApi) {
        this.notionClient = notionClient;
        this.s3Manager = s3Manager;
        this.msgAuditService = msgAuditService;
        this.markdownOutputService = markdownOutputService;
        this.configQueryApi = configQueryApi;
    }

    @Override
    protected NotionClient getNotionClient() {
        return notionClient;
    }


    @Override
    protected S3Manager getS3Manager() {
        return s3Manager;
    }

    @Override
    protected WxCpMsgAuditService getMsgAuditService() {
        return msgAuditService;
    }

    @Override
    protected IChatOpenListService getChatOpenListService() {
        return null;
    }
    
    @Override
    protected MarkdownOutputService getMarkdownOutputService() {
        return markdownOutputService;
    }
    
    @Override
    protected ConfigQueryApi getConfigQueryApi() {
        return configQueryApi;
    }

    /**
     * 处理图片类型的消息并同步到Notion
     * 
     * 该方法接收从微信获取的图片消息，验证消息时效性，
     * 然后构建相应的Notion Block并发送到Notion平台。
     * 
     * @param message       包含图片信息的JSON消息对象
     * @param sdk           企业微信SDK实例ID
     * @param messageConfig 消息配置信息，包含用户ID和Notion连接参数
     * @param tagList       消息标签列表
     * @throws WxErrorException 微信API调用异常
     * @throws IOException      IO异常
     */
    @Override
    public void process(JSONObject message, Long sdk, String unionId, List<String> tagList)
            throws WxErrorException, IOException {
        // 记录处理图片消息的日志
        log.info("process image, msgContent={}", message);

        // 1. 检查消息时效性
        boolean isValidTime = isMessageValid(message);

        // 2. 构建Block
        Block block;
        if (isValidTime) {
            block = buildImageBlock(message, unionId, sdk, false);
        } else {
            block = Block.paragraph(RichTexts.builder()
                    .rich_text(Collections.singletonList(RichText.simpleText("图片消息已过期，无法同步"))).build());
        }
        String title = "图片_"+ LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        // 3. 根据用户配置将构建好的Block发送到对应平台
        super.output(unionId, title, MsgTypeEnum.IMAGE, Collections.singletonList(block),
                tagList.toArray(new String[0]));
    }

}
