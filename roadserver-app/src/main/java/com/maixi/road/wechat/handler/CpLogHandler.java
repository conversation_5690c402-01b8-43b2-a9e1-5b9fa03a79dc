package com.maixi.road.wechat.handler;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Slf4j
@Component
public class CpLogHandler extends AbstractHandler {

    @Override
    public WxCpXmlOutMessage handle(WxCpXmlMessage wxMessage, Map<String, Object> context, WxCpService cpService,
                                    WxSessionManager sessionManager) {
        try {
            log.info("企业微信消息 - 类型: {}, 事件类型: {}, 事件Key: {}, 发送者: {}, 接收者: {}, 内容长度: {}, 消息ID: {}, 创建时间: {}",
                    wxMessage.getMsgType(),
                    wxMessage.getEvent(),
                    wxMessage.getEventKey(),
                    wxMessage.getFromUserName(), 
                    wxMessage.getToUserName(), 
                    wxMessage.getContent() != null ? wxMessage.getContent().length() : 0,
                    wxMessage.getMsgId(), 
                    wxMessage.getCreateTime());
        } catch (Exception e) {
            log.error("处理消息时发生异常", e);
        }
        
        return null;
    }


}
