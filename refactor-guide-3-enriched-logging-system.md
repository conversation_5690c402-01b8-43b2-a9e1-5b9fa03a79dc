# 重构指南 3：增强日志系统

## 重构原因分析

当前项目中的日志记录相对简单，主要存在以下问题：

1. **日志粒度不够**：缺少详细的业务操作日志
2. **缺乏结构化日志**：日志内容不便于后续分析和检索
3. **缺少关键指标监控**：无法从日志中快速获取性能和业务指标
4. **异常日志不完整**：缺少上下文信息和堆栈跟踪的详细信息
5. **日志级别使用不当**：没有充分利用不同级别的日志记录

在`S3CloudFunctionApiImpl`中，日志记录仅包含基本信息：
```java
log.info("request = {}", JSON.toJSONString(request));
log.error("图片转存触发异常,message={}", error.getMessage());
```

## 具体示例

当前的日志记录方式无法满足生产环境监控需求，例如：
- 无法追踪特定用户操作的完整流程
- 无法快速定位性能瓶颈
- 缺乏对关键业务指标的统计

## 完整重构方案

### 第一步：引入结构化日志框架

使用SLF4J和Logback的结构化日志功能：

```xml
<!-- 在 pom.xml 中添加依赖 -->
<dependency>
    <groupId>net.logstash.logback</groupId>
    <artifactId>logstash-logback-encoder</artifactId>
    <version>7.4</version>
</dependency>
```

### 第二步：创建日志上下文工具类

```java
// 在 road-common 模块中创建
@Component
public class LogContext {
    private static final MDCContextFactory mdcContextFactory = new MDCContextFactory();
    
    public static void setTraceId(String traceId) {
        MDC.put("traceId", traceId);
    }
    
    public static void setUserContext(String userId, String unionId) {
        MDC.put("userId", userId);
        MDC.put("unionId", unionId);
    }
    
    public static void clear() {
        MDC.clear();
    }
}
```

### 第三步：创建业务日志注解

```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface BusinessLog {
    String operation();
    String module() default "";
    boolean logParams() default true;
    boolean logResult() default true;
}
```

### 第四步：创建日志切面

```java
// 在 road-common 或 roadserver-app 模块中创建
@Aspect
@Component
@Slf4j
public class BusinessLogAspect {
    
    @Around("@annotation(businessLog)")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint, BusinessLog businessLog) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        
        // 设置日志上下文
        String traceId = UUID.randomUUID().toString();
        LogContext.setTraceId(traceId);
        
        long startTime = System.currentTimeMillis();
        Object result = null;
        
        try {
            // 记录方法开始
            if (businessLog.logParams()) {
                log.info("业务操作开始 - 模块: {}, 操作: {}, 类: {}, 方法: {}, 参数: {}",
                        businessLog.module(), businessLog.operation(), className, methodName,
                        Arrays.toString(joinPoint.getArgs()));
            }
            
            result = joinPoint.proceed();
            
            // 记录方法结束
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            if (businessLog.logResult()) {
                log.info("业务操作完成 - 模块: {}, 操作: {}, 耗时: {}ms, 结果: {}",
                        businessLog.module(), businessLog.operation(), duration, result);
            } else {
                log.info("业务操作完成 - 模块: {}, 操作: {}, 耗时: {}ms",
                        businessLog.module(), businessLog.operation(), duration);
            }
            
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.error("业务操作异常 - 模块: {}, 操作: {}, 耗时: {}ms, 异常: {}",
                    businessLog.module(), businessLog.operation(), duration, e.getMessage(), e);
            throw e;
        } finally {
            LogContext.clear();
        }
    }
}
```

### 第五步：重构现有业务逻辑

修改`S3CloudFunctionApiImpl`以使用增强日志：

```java
@Service
@RequiredArgsConstructor
public class S3CloudFunctionApiImpl implements S3CloudFunctionApi {
    
    private final CloudFunctionServiceFactory serviceFactory;
    
    @BusinessLog(operation = "图片转存", module = "S3云函数")
    @Override
    public S3CloudRS transformPicture(String unionId, S3CloudRQ request) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("transformPicture");
        
        try {
            log.info("开始处理图片转存请求 - 用户ID: {}, 请求参数: {}", unionId, request);
            
            // 设置用户上下文
            LogContext.setUserContext(unionId, request.getS3Config().getUserId());
            
            // 原有业务逻辑
            CloudFunctionService service = serviceFactory.getService(request.getS3Config().getProvider());
            CloudFunctionResponse response = service.invokeFunction(convertToCloudFunctionRequest(request));
            
            if (response == null) {
                log.warn("图片转存结果为空 - 用户ID: {}", unionId);
                throw new BusinessException(1001, "图片转存结果为空");
            }
            
            S3CloudRS s3Response = convertToS3CloudRS(response);
            log.info("图片转存成功 - 用户ID: {}, 处理结果: {}", unionId, s3Response);
            
            return s3Response;
        } catch (Exception e) {
            log.error("图片转存失败 - 用户ID: {}, 请求参数: {}, 异常信息: {}", 
                    unionId, request, e.getMessage(), e);
            throw new SystemException(500, "图片处理服务异常", e);
        } finally {
            stopWatch.stop();
            log.info("图片转存请求处理完成 - 用户ID: {}, 总耗时: {}秒", 
                    unionId, stopWatch.getTotalTimeSeconds());
        }
    }
}
```

### 第六步：配置结构化日志格式

在`application.yml`中添加配置：
```yaml
logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  level:
    com.maixi.road: INFO
    org.springframework: WARN
```

### 第七步：创建监控指标日志

```java
@Component
public class MetricsLogger {
    
    private static final Logger metricsLogger = LoggerFactory.getLogger("metrics");
    
    public void logOperationMetrics(String operation, long duration, boolean success) {
        metricsLogger.info("OPERATION_METRICS|{}|{}|{}", 
                operation, duration, success ? "SUCCESS" : "FAILURE");
    }
    
    public void logBusinessMetric(String metricName, String value) {
        metricsLogger.info("BUSINESS_METRIC|{}|{}", metricName, value);
    }
}
```

## 预期收益

1. **结构化日志输出**：日志内容格式统一，便于解析和分析
2. **完整的业务追踪**：通过traceId可追踪完整业务流程
3. **性能监控能力**：能够快速识别性能瓶颈
4. **更好的异常诊断**：异常日志包含完整的上下文信息
5. **业务指标统计**：便于生成业务报表和监控仪表板
6. **易于维护**：统一的日志规范和工具类