package com.maixi.road.common.core.model.response;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustomConfigVo {

    /**
     * 总是使用图床，若为是，则所有平台都使用图床
     * 若为否，则除了小红书和少数派及公共网页，都不使用图床
     */
    private Boolean alwaysUsePicCloud;

    /**
     * 图床优先使用 Cloudinary
     */
    private Boolean alwaysUseCloudinary;

    /**
     * 支持通用网页
     */
    private Boolean supportAllSite;

    /**
     * 是否不需要图标
     */
    private Boolean noIcon;

    /**
     * 是否不需要封面
     */
    private Boolean noCover;

    /**
     * 是否将快速剪藏设置为默认页面
     */
    private Boolean quickClipAsEnterPage;

    /**
     * 是否保存到 obsidian
     */
    private Boolean toObsidian;

    /**
     * 是否保存链接文本
     */
    private Boolean saveLinkText;
}
