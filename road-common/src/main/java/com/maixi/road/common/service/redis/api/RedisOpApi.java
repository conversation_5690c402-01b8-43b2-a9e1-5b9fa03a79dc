package com.maixi.road.common.service.redis.api;

import java.util.concurrent.Future;

import com.maixi.road.common.core.model.dto.ResolveRS;

public interface RedisOpApi {

    /**
     * 保存剪切结果
     * @param url
     * @param blocks
     */
    void saveClipResult(String url, ResolveRS resolveRS);

    /**
     * 获取剪切结果
     * @param url
     * @return
     */
    Future<ResolveRS> getClipResult(String url);
}
