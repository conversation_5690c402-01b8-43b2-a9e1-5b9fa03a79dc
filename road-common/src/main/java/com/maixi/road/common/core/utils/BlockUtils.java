package com.maixi.road.common.core.utils;

import java.util.List;

import com.google.common.collect.Lists;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Bookmark;
import com.maixi.road.common.integration.notion.model.block.Callout;

/**
 * Block构建工具类
 * 提供常用的Block构建方法
 *
 * <AUTHOR>
 */
public class BlockUtils {

    /**
     * 构建错误信息的Block列表
     * 包含错误提示和原文链接
     *
     * @param url    原文链接URL
     * @param tipMsg 错误提示消息
     * @return Block列表
     */
    public static List<Block> buildErrorBlocks(String url, String tipMsg) {
        List<Block> errorBlocks = Lists.newArrayList();

        // 创建错误信息的Callout Block
        Callout calloutBlock = Callout.buildTip(tipMsg);
        Block calloutBlockWrapper = Block.callout(calloutBlock);
        errorBlocks.add(calloutBlockWrapper);

        // 创建原文链接的Bookmark Block
        Bookmark bookmarkBlock = new Bookmark(url, url);
        Block bookmarkBlockWrapper = Block.buildBookmark(bookmarkBlock);
        errorBlocks.add(bookmarkBlockWrapper);

        return errorBlocks;
    }

    /**
     * 构建提示信息的Callout Block
     *
     * @param tipMsg 提示消息
     * @return Callout Block
     */
    public static Block buildTipBlock(String tipMsg) {
        Callout calloutBlock = Callout.buildTip(tipMsg);
        return Block.callout(calloutBlock);
    }

    /**
     * 构建书签Block
     *
     * @param url 链接URL
     * @return Bookmark Block
     */
    public static Block buildBookmarkBlock(String url) {
        Bookmark bookmarkBlock = new Bookmark(url, url);
        return Block.buildBookmark(bookmarkBlock);
    }

    /**
     * 构建书签Block
     *
     * @param url   链接URL
     * @param title 书签标题
     * @return Bookmark Block
     */
    public static Block buildBookmarkBlock(String url, String title) {
        Bookmark bookmarkBlock = new Bookmark(url, title);
        return Block.buildBookmark(bookmarkBlock);
    }
}