package com.maixi.road.common.integration.notion.constants;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.regex.Pattern;

import com.google.common.collect.Lists;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.page.Icon;

/**
 * <AUTHOR> * @since 2023/7/26
 **/
public class NotionConstants {

    public static final boolean isWindows = System.getProperty("os.name").toLowerCase().contains("win");
    public static final boolean isLinux = System.getProperty("os.name").toLowerCase().contains("linux");

    public static final String TEST_UNION_ID = "osb1655YhzjZS4MrrpHnMOfiAY8Q";
    public static final String TEST_OPEN_ID = "o-tho5dc_Qo5jHlZyp438le8CIpE";

    public static final String WX_USER_SECRET_PREFIX = "绑定口令:春暖花开@secret:";
    public static final int WX_USER_SECRET_PREFIX_LENGTH = WX_USER_SECRET_PREFIX.length();

    public static final Pattern domainPattern = Pattern.compile("^(https?://)?(www\\.)?(mp\\.weixin\\.qq\\.com|sspai\\.com|(m\\.)?douban\\.com|bilibili\\.com|woshipm\\.com|xiaohongshu\\.com|xhslink\\.com|zhuanlan\\.zhihu\\.com|zhihu\\.com)(/\\S*)?$");
    public static final Pattern shortLinkPattern = Pattern.compile("(https?://)?(www\\.)?(xhslink\\.com)(/[a-zA-Z0-9]+)?(/[a-zA-Z0-9]+)?，");

    public static final Pattern webUrlRegex = Pattern.compile("(https?://)?((?!-)[a-zA-Z0-9-]{1,63}(?<!-)\\.)+[a-zA-Z]{2,}(/[-\\w._~:/?#@!$&'()*+,;=]*)?");

    public static final String SPACE = " ";
    private static final byte[] bytes = {(byte) 0xC2, (byte) 0xA0};
    public static final String UTFSpace = new String(bytes, StandardCharsets.UTF_8);
    public static final String NOTION_VERSION = "2022-06-28";
    public static final List<String> LANGUAGE_LIST = Lists.newArrayList("abap", "arduino", "bash", "basic", "c", "clojure", "coffeescript", "c++",
            "c#", "css", "dart", "diff", "docker", "elixir", "elm", "erlang", "flow", "fortran", "f#", "gherkin",
            "glsl", "go", "graphql", "groovy", "haskell", "html", "java", "javascript", "json", "julia", "kotlin",
            "latex", "less", "lisp", "livescript", "lua", "makefile", "markdown", "markup", "matlab", "mermaid",
            "nix", "objective-c", "ocaml", "pascal", "perl", "php", "plain text", "powershell", "prolog", "protobuf",
            "python", "r", "reason", "ruby", "rust", "sass", "scala", "scheme", "scss", "shell", "swift", "typescript",
            "vb.net", "verilog", "vhdl", "visual basic", "webassembly", "xml", "yaml", "java/c/c++/c#");

    public static final String priKey = "-----BEGIN RSA PRIVATE KEY-----\n" +
            "MIIEpAIBAAKCAQEArQUF71C8aB2HiqV4iGMObEUhlgHMgvqpd6PfdBTpx1rO2cuJ\n" +
            "MgUSnDXc6gfOX5xXPTgi12QvYCIfX7GwrjRAJBoMgXtemC84sZg+/YGcf+UGZc00\n" +
            "UroZOg3Y2jjKSLhcd9tt4m7UatSFqWT2TORMcqSav2d3PjrQ/foazE54PMCQV24V\n" +
            "wmqSmQdlI8/DMA8Cm2GE5B875sRLyzBpb+JsOFzG3vrLrmlet98QecpqSF4p15T1\n" +
            "eMXGouaTiQfCXeQKv+2WmzlZJb9VYi1hpQjkLb7hz3eqfGiO0kTk9twLnnh3vNl+\n" +
            "BhUi/2jfVW0/fYB28yKAHh0ZyVI8Ps60wn6POQIDAQABAoIBAQCRK4HLRmaOxRgQ\n" +
            "0L3NUa+sUGTZxDCgRUkjxS+b6mAskJi2TPyUJ4Vx5A+znVEfD8Jy70Lsjnbhpj/E\n" +
            "vwaLY/PdVq3fhm/cKOLcv+06LyHsfXFcwUyBjPvVpgCpOLUhRu7u8gHE11ELQb3U\n" +
            "X434/iTWnB3rMxjt8dOhBdSuT7i630YQO7kSilWuzC8FdfjgSsw1by2P1ipIcIA0\n" +
            "hvQW4swb0L88KxdzbTEp/uGkhSbGFS9+JapJ67joSGzTnClveuKJoLYqw7ypS1Ws\n" +
            "yu73xQDokLyF1XKKIToBQDj8OKEklv93Jz8QKGLXdrSHTMLlXTNfd+roicRe7S3W\n" +
            "UAOL2CxxAoGBANNJfw8YYCEkYK+RfK7947wi69O/f7fCbkHaED/TRwKYbgjS7fuO\n" +
            "O/lc3yDFKys3C2ZIs10CadDgYy4tFJ+5266zBCScmBBorNdJpsrLNxnT/t7siJIm\n" +
            "Bvr8dGp8LyFaaLiZTCRI7ViLTY78N3jvCmpMdqRtMYOY2bn/LSGOwaF/AoGBANGi\n" +
            "YcUtiQR93EuthumPIf/Cw+2+kxEQHjTS+upJnOg041APURV7WU3uthBHVW5aZa1A\n" +
            "dSM39yBBeTkYDGadFcbdgM7Pc3UcTnE+8MsEjsQMiNjc2pveSZKhwTwqx0vliMp9\n" +
            "CbygM03VjLkusIEtwV96zPUbu/vmm5IngbjprbtHAoGABo0Q03b+BAkMtmTcaNCW\n" +
            "bw7mIPqfCyOMJ56LQm9alEoFn2Bq4JHYajLm+C0k2YIraFB4vzmD7mYGrTfYm9s3\n" +
            "ZINIbZotXNTiBwrRkRgb7UEVdfP/iAiFzuJk+ahADdivxol6QuW/9i7biMNpNOa0\n" +
            "e/dC9nYsQthKZ+nxjEjQ2CECgYEAgECnLjoIjM3vnAEi+/tMoqvmsKoaUaMZx9mY\n" +
            "FwRIUawhsdZdFhZr8YjsrBLoSmZVtGQuBgj0UTCdWHfFBnZuX0ceyegTS/+wYgpO\n" +
            "yPYV3g2JH5wmxpt9SY9aqFPHsEBSqwdKkLM2EBVpTdYpuC5lEuQPRpncLWXNoeI3\n" +
            "tAiK+HsCgYBDZADtk4Lu2PUXEfWNdrSn3/i3Nu1EeIhkpQp1ixDfbfsQ5vvmy7Eu\n" +
            "mQitzjXJmJuKOyYa+ujGCPc4lkS8V3V6oW/RojymjXHXXxpRGfo3n5ahDjEQz5E9\n" +
            "PI9xzVLpHT39/CTd20kMHMuhZrw4se9N9VJ9LqZkIYfqrBUAXe65YQ==\n" +
            "-----END RSA PRIVATE KEY-----\n";

    public static final String COVER_ZH_QA = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397052/NotionMpClipper/cover_zhihu.jpg";
    public static final String COVER_WB = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397048/NotionMpClipper/cover_weibo.jpg";
    public static final String COVER_ZH_ZL = "https://notionclipper.fun/images/cover/cover_zhzl.jpg";
    public static final String COVER_XHS = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397043/NotionMpClipper/cover_xiaohongshu.png";
    public static final String COVER_MP = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397038/NotionMpClipper/cover_mp.png";
    public static final String COVER_XYZ = "https://res.cloudinary.com/dwal1nlws/image/upload/v1733837632/wexin/mp/c91fd6b55704303956002aba0e7d5b0c.png";
    public static final String COVER_XCF = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397033/NotionMpClipper/cover_xiachufang.png";
    public static final String COVER_JK = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397021/NotionMpClipper/cover_jike.png";
    public static final String COVER_SSPAI = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397031/NotionMpClipper/cover_sspai.png";
    public static final String COVER_DOUBAN = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397018/NotionMpClipper/cover_dbgroup.png";
    public static final String COVER_DOUBAN_NOTE = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397044/NotionMpClipper/cover_dbnote.png";
    public static final String COVER_DOUBAN_MOVIE = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1726565403/NotionMpClipper/cover_douban_movie.png";
    public static final String COVER_DOUBAN_BOOK = "https://res.cloudinary.com/dwal1nlws/image/upload/v1730294526/wexin/mp/34ffb426012d010407dc62af44bf9d9e.png";
    public static final String COVER_BILIBILI = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397778/NotionMpClipper/cover_bilibili.png";
    public static final String COVER_PM = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397044/NotionMpClipper/cover_pm.png";
    public static final String COVER_CSDN = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397044/NotionMpClipper/cover_csdn.png";
    public static final String COVER_JUEJIN = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397044/NotionMpClipper/cover_juejin.png";
    public static final String COVER_SMZDM = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1727840296/NotionMpClipper/ur152ordantwca04yhrs.png";
    public static final String COVER_XUEQIU = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1727870098/NotionMpClipper/ejlscwjvn2efdianp5qs.png";
    public static final String COVER_UTAG = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1727965891/NotionMpClipper/xa18c9omvkooz2ythljs.png";
    public static final String COVER_BOOKMARK = "https://res.cloudinary.com/dwal1nlws/image/upload/v1731799119/wexin/mp/e7bd280606f2136b4a159bd57ffaff0b.png";
    public static final String COVER_POST = "https://res.cloudinary.com/dwal1nlws/image/upload/v1731799143/wexin/mp/63d9e30d692ce7ca5f71363c4ae7f578.png";
    public static final String COVER_YOUTUBE = "https://res.cloudinary.com/dwal1nlws/image/upload/v1731800809/wexin/mp/ffb547402bc4138bb1a768f07860e79d.png";
    public static final String COVER_X = "https://res.cloudinary.com/dwal1nlws/image/upload/v1731800844/wexin/mp/5d3dc3d9bab3b8e8dcf2a7f5bb807bc7.png";
    public static final String COVER_DOUYIN = "https://res.cloudinary.com/dwal1nlws/image/upload/v1752377325/ftz3k1xqe3buit3lwvti.png";
    public static final String COVER_DEFAULT = "https://res.cloudinary.com/dwal1nlws/image/upload/v1731799143/wexin/mp/63d9e30d692ce7ca5f71363c4ae7f578.png";

    public static final String ICON_ZH = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397573/NotionMpClipper/icon_zhihu.png";
    public static final String ICON_MP = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397573/NotionMpClipper/icon_mp.png";
    public static final String ICON_XYZ = "https://res.cloudinary.com/dwal1nlws/image/upload/v1733838489/wexin/mp/175f2b3cc9c5d42e069b992a621f31f2.png";
    public static final String ICON_XHS = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397573/NotionMpClipper/icon_xiaohongshu.png";
    public static final String ICON_WB = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397573/NotionMpClipper/icon_weibo.png";
    public static final String ICON_XCF = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397573/NotionMpClipper/icon_xiachufang.png";
    public static final String ICON_JK = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397573/NotionMpClipper/icon_jike.png";
    public static final String ICON_SSPAI = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397573/NotionMpClipper/icon_sspai.png";
    public static final String ICON_DOUBAN = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397572/NotionMpClipper/icon_douban.png";
    public static final String ICON_BILIBILI = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397604/NotionMpClipper/icon_bilibili.png";
    public static final String ICON_DEFAULT = "https://res.cloudinary.com/dwal1nlws/image/upload/v1731164499/wexin/mp/da8b9725c2dc50080c4ca5e68054a594.png";
    public static final String ICON_PM = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397604/NotionMpClipper/icon_pm.png";
    public static final String ICON_CSDN = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397604/NotionMpClipper/icon_csdn.png";
    public static final String ICON_JUEJIN = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1714397604/NotionMpClipper/icon_juejin.png";
    public static final String ICON_SMZDM = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1727840310/NotionMpClipper/r16qpnklg45sfyybw9wh.png";
    public static final String ICON_XUEQIU = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1727870107/NotionMpClipper/tjtv9lj2znalmnwcnknt.png";
    public static final String ICON_UTAG = "https://res.cloudinary.com/do7ds3qiw/image/upload/v1727965891/NotionMpClipper/vznaj1khvu7tcjer9r05.png";
    public static final String ICON_BOOKMARK = "https://res.cloudinary.com/dwal1nlws/image/upload/v1731799051/wexin/mp/76ca2a969535579a1ef4037810228ed4.png";
    public static final String ICON_POST = "https://res.cloudinary.com/dwal1nlws/image/upload/v1731799093/wexin/mp/c9fbefe93dde2e92bb136a4e735b331e.png";
    public static final String ICON_YOUTUBE = "https://res.cloudinary.com/dwal1nlws/image/upload/v1731800875/wexin/mp/7f968ee0d96b5e15b702c5270b662dd9.png";
    public static final String ICON_X = "https://res.cloudinary.com/dwal1nlws/image/upload/v1731800889/wexin/mp/a77d82706a457fda5cbb2a7389e361a2.png";
    public static final String ICON_DOUYIN = "https://res.cloudinary.com/dwal1nlws/image/upload/v1752377354/puocnpim3ie1lzb1lwrc.png";

    public static final Pattern MP_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(mp\\.weixin\\.qq\\.com)(/\\S*)?$");
    public static final Pattern ZH_PATTERN = Pattern.compile("^(https://)?(www\\.)?(zhuanlan\\.zhihu\\.com)(/\\S*)?$");
    public static final Pattern XCF_PATTERN = Pattern.compile("^(https://)?(www\\.)?(xiachufang\\.com/recipe/\\d+)(/\\S*)?$");
    public static final Pattern ZHQA_PATTERN = Pattern.compile("^(https://)?(www\\.)?(zhihu\\.com/question/\\d+/answer/\\d+)(/\\S*)?");
    public static final Pattern XHS_PATTERN = Pattern.compile("^(https://)?(www\\.)?(xiaohongshu\\.com)(/\\S*)?$");
    public static final Pattern XHS_LINK_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(xhslink\\.com)(/\\S*)?$");
    public static final Pattern WEIBO_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(weibo\\.com/\\d+)(/\\S*)?$");
    public static final Pattern WEIBO_TOP_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(weibo\\.com/ttarticle)(/\\S*)?$");
    public static final Pattern JIKE_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(m\\.okjike\\.com/originalPosts)(/\\S*)?$");
    public static final Pattern XyuZhou_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(xiaoyuzhoufm\\.com/episode)(/\\S*)?$");
    public static final Pattern JIKE_PATTERN2 = Pattern.compile("^(https?://)?(www\\.)?(web\\.okjike\\.com/originalPost)(/\\S*)?$");
    public static final Pattern SSPAI_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(sspai\\.com/post/\\d+)$");
    public static final Pattern DOUBAN_GROUP_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(m\\.)?(douban\\.com/group/topic/\\d+)(/\\S*)?");
    public static final Pattern DOUBAN_NOTE_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(m\\.)?(douban\\.com/note/\\d+)(/\\S*)?");
    public static final Pattern DOUBAN_MOVIE_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(m\\.)?(movie.douban\\.com/review/\\d+)(/\\S*)?");
    public static final Pattern DOUBAN_BOOK_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(m\\.)?(douban\\.com/book/review/\\d+)(/\\S*)?");
    //https://book.douban.com/review/15816226/
    public static final Pattern DOUBAN_BOOK2_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(book\\.douban\\.com/review/\\d+)(/\\S*)?");
    public static final Pattern DOUBAN_BOOK3_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(douban\\.com/doubanapp/dispatch/review/\\d+)(/\\S*)?");
    public static final Pattern PM_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(woshipm\\.com)(/\\S*)?");
    public static final Pattern BILIBILI_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(m\\.)?(bilibili\\.com/video)(/\\S*)?");
    public static final Pattern BILIBILI_SHORT_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(b23\\.tv)(/\\S*)?");
    public static final Pattern CSDN_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(blog\\.csdn\\.net/[a-zA-Z0-9_]+/article/details/[0-9]+)");
    public static final Pattern JUEJIN_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(juejin\\.cn/post/[0-9]+)");
    // 什么值得买：https://post.smzdm.com/p/apm8d3w0/
    public static final Pattern SMZDM_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(post\\.smzdm\\.com/p/[a-zA-Z0-9_]+)");
    // https://xueqiu.com/1760673340/305217737
    public static final Pattern XUEQIU_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(xueqiu\\.com/[0-9]+/[0-9]+)");
    // https://utgd.net/article/20856/
    public static final Pattern UNTAG_PATTERN = Pattern.compile("^(https?://)?(www\\.)?(utgd\\.net/article/[0-9]+)");

    public static Icon icon(String origin) {
        if (OriginTypeEnum.RB.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_XHS);
        } else if (OriginTypeEnum.ZH.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_ZH);
        } else if (OriginTypeEnum.WX.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_MP);
        } else if (OriginTypeEnum.XCF.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_XCF);
        } else if (OriginTypeEnum.WB.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_WB);
        } else if (OriginTypeEnum.JK.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_JK);
        } else if (OriginTypeEnum.SSPAI.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_SSPAI);
        } else if (OriginTypeEnum.DOUBAN_GROUP.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_DOUBAN);
        } else if (OriginTypeEnum.DOUBAN_BOOK.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_DOUBAN);
        } else if (OriginTypeEnum.BILIBILI.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_BILIBILI);
        } else if (OriginTypeEnum.DOUBAN_NOTE.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_DOUBAN);
        } else if (OriginTypeEnum.DOUBAN_MOVIE.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_DOUBAN);
        } else if (OriginTypeEnum.PM.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_PM);
        } else if (OriginTypeEnum.JUEJIN.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_JUEJIN);
        } else if (OriginTypeEnum.CSDN.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_CSDN);
        } else if (OriginTypeEnum.SMZDM.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_SMZDM);
        } else if (OriginTypeEnum.XUEQIU.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_XUEQIU);
        } else if (OriginTypeEnum.UNTAG.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_UTAG);
        } else if (OriginTypeEnum.XyuZhou.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_XYZ);
        } else if (OriginTypeEnum.DOUYIN.getName().equals(origin)) {
            return Icon.buildByFile(NotionConstants.ICON_DOUYIN);
        }
        return Icon.buildByFile(NotionConstants.ICON_DEFAULT);
    }

}