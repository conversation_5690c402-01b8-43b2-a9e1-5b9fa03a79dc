package com.maixi.road.clipper.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * CSS样式解析工具类
 * 用于解析HTML元素的CSS样式属性
 * 
 * <AUTHOR>
 */
@Slf4j
public class CSSStyleParser {

    /**
     * 私有构造函数，防止实例化
     */
    private CSSStyleParser() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    // 匹配CSS尺寸值的正则表达式，支持px、em、rem、%等单位
    private static final Pattern SIZE_PATTERN = Pattern.compile("([0-9]*\\.?[0-9]+)(px|em|rem|%|pt|pc|in|cm|mm|ex|ch|vw|vh|vmin|vmax)?", Pattern.CASE_INSENSITIVE);

    /**
     * 从CSS样式字符串中提取指定属性的值
     * 
     * @param style CSS样式字符串
     * @param property 要提取的属性名（如width、height等）
     * @return 属性值，如果未找到返回null
     */
    public static String extractCSSProperty(String style, String property) {
        if (StringUtils.isBlank(style) || StringUtils.isBlank(property)) {
            return null;
        }

        try {
            String lowerStyle = style.toLowerCase();
            String lowerProperty = property.toLowerCase();
            String searchPattern = lowerProperty + ":";
            
            int startIndex = lowerStyle.indexOf(searchPattern);
            if (startIndex == -1) {
                return null;
            }

            // 跳过属性名和冒号
            startIndex += searchPattern.length();

            // 跳过空格
            while (startIndex < lowerStyle.length() && Character.isWhitespace(lowerStyle.charAt(startIndex))) {
                startIndex++;
            }

            // 找到值的结束位置
            int endIndex = startIndex;
            while (endIndex < lowerStyle.length() && lowerStyle.charAt(endIndex) != ';') {
                endIndex++;
            }

            if (startIndex >= endIndex) {
                return null;
            }

            return style.substring(startIndex, endIndex).trim();
        } catch (Exception e) {
            log.warn("解析CSS属性{}时出错: {}", property, style, e);
            return null;
        }
    }

    /**
     * 解析CSS尺寸值，返回像素值
     * 支持px、em、rem、%等单位，对于非px单位会进行近似转换
     * 
     * @param sizeValue CSS尺寸值（如"100px"、"2em"、"50%"等）
     * @return 像素值，解析失败返回-1
     */
    public static double parseSizeToPixels(String sizeValue) {
        if (StringUtils.isBlank(sizeValue)) {
            return -1;
        }

        try {
            String trimmedValue = sizeValue.trim().toLowerCase();
            Matcher matcher = SIZE_PATTERN.matcher(trimmedValue);
            
            if (!matcher.find()) {
                return -1;
            }

            double value = Double.parseDouble(matcher.group(1));
            String unit = matcher.group(2);

            // 如果没有单位，默认为px
            if (unit == null) {
                return value;
            }

            // 根据单位进行转换
            return switch (unit) {
                case "px" -> value;
                case "em", "rem" ->
                    // 假设1em = 16px（浏览器默认字体大小）
                        value * 16;
                case "%" ->
                    // 对于百分比，无法准确转换，返回-1表示无法判断
                        -1;
                case "pt" ->
                    // 1pt = 1.33px
                        value * 1.33;
                case "pc" ->
                    // 1pc = 16px
                        value * 16;
                case "in" ->
                    // 1in = 96px
                        value * 96;
                case "cm" ->
                    // 1cm = 37.8px
                        value * 37.8;
                case "mm" ->
                    // 1mm = 3.78px
                        value * 3.78;
                default ->
                    // 其他单位暂不支持
                        -1;
            };
        } catch (Exception e) {
            log.warn("解析CSS尺寸值时出错: {}", sizeValue, e);
            return -1;
        }
    }

    /**
     * 从元素的style属性中获取宽度（像素值）
     * 
     * @param style CSS样式字符串
     * @return 宽度像素值，解析失败返回-1
     */
    public static double getWidthFromStyle(String style) {
        String widthValue = extractCSSProperty(style, "width");
        return parseSizeToPixels(widthValue);
    }

    /**
     * 从元素的style属性中获取高度（像素值）
     * 
     * @param style CSS样式字符串
     * @return 高度像素值，解析失败返回-1
     */
    public static double getHeightFromStyle(String style) {
        String heightValue = extractCSSProperty(style, "height");
        return parseSizeToPixels(heightValue);
    }

    /**
     * 检查图片尺寸是否为小图片
     * 
     * @param style CSS样式字符串
     * @param minWidth 最小宽度阈值（像素）
     * @param minHeight 最小高度阈值（像素）
     * @return true表示是小图片，应该被过滤
     */
    public static boolean isSmallImage(String style, double minWidth, double minHeight) {
        if (StringUtils.isBlank(style)) {
            return false;
        }

        double width = getWidthFromStyle(style);
        double height = getHeightFromStyle(style);

        // 如果宽度或高度小于阈值，则认为是小图片
        // 注意：-1表示无法解析，这种情况下不过滤
        if (width > 0 && width < minWidth) {
            log.debug("图片宽度{}px小于阈值{}px，将被过滤", width, minWidth);
            return true;
        }

        if (height > 0 && height < minHeight) {
            log.debug("图片高度{}px小于阈值{}px，将被过滤", height, minHeight);
            return true;
        }

        return false;
    }

    /**
     * 检查图片宽度是否为小图片（使用默认高度阈值）
     * 
     * @param style CSS样式字符串
     * @param minWidth 最小宽度阈值（像素）
     * @return true表示是小图片，应该被过滤
     */
    public static boolean isSmallImageByWidth(String style, double minWidth) {
        return isSmallImage(style, minWidth, 0); // 高度阈值设为0，只检查宽度
    }
}
