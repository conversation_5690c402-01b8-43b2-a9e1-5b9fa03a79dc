package com.maixi.road.clipper.service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.maixi.road.clipper.ClipperApi;
import com.maixi.road.clipper.constant.ClipperConstants;
import com.maixi.road.clipper.service.core.ClipperCoreService;
import com.maixi.road.clipper.strategy.OutputStrategyFactory;
import com.maixi.road.clipper.util.LinkExtractor;
import com.maixi.road.clipper.util.TagProcessor;
import com.maixi.road.common.core.enums.error.ClipperErrCodeEnum;
import com.maixi.road.common.core.exception.ClipperException;
import com.maixi.road.common.core.model.dto.ClipperResponse;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.core.model.dto.ResolveRS;
import com.maixi.road.common.core.model.dto.TextMessageRequest;
import com.maixi.road.common.core.model.dto.UserConfig;
import com.maixi.road.common.core.utils.BlockUtils;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 剪藏API实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ClipperApiImpl implements ClipperApi {

    @Resource
    private ClipperCoreService clipperService;
    @Resource
    private OutputStrategyFactory outputStrategyFactory;
    @Resource
    private ConfigQueryApi configQueryApi;

    /**
     * 虚拟线程池执行器，用于异步处理
     */
    private static final ExecutorService EXECUTOR = Executors.newVirtualThreadPerTaskExecutor();

    /**
     * 处理来自小程序的URL解析请求
     * 
     * @param url    要解析的URL
     * @param userId 用户ID
     * @return 解析结果
     */
    @Override
    public ClipperResponse<ResolveRS> parseUrl(String url, String userId) {
        try {
            ResolveRS result = clipperService.parseUrl(url, userId);
            return ClipperResponse.success(result);
        } catch (ClipperException e) {
            return ClipperResponse.fail(e.getCode(), e.getMsg());
        }
    }

    /**
     * 处理来自收藏助手的Link消息
     * 
     * @param unionId 用户ID
     * @param tags    标签数组
     * @param link    链接URL
     */
    @Override
    public void parseUrlThenSave(String unionId, String[] tags, String link) {
        try {
            // 解析链接并记录日志
            ResolveRS result = clipperService.parseUrl(link, unionId);

            // 构建文章请求对象
            ResolveFormRQ rq = buildResolveFormRQ(result, tags);

            // 添加延迟，避免解析正文还没完成就开始尝试获取正文
            Thread.sleep(ClipperConstants.PARSE_DELAY_MS);

            // 提交文章
            clipperService.saveContent(rq, unionId);

        } catch (Exception e) {
            log.error("解析链接失败, link={}, userId={}", link, unionId, e);
            handleParseFailure(link, tags, unionId, e);
        }
    }

    @Override
    public ClipperResponse<Boolean> saveContent(ResolveFormRQ article, String userId) {
        try {
            boolean result = clipperService.saveContent(article, userId);
            return ClipperResponse.success(result);
        } catch (Exception e) {
            log.error("提交文章异常, title={}, link={}, userId={}",
                    article.getTitle(), article.getLink(), userId, e);
            return ClipperResponse.fail(ClipperErrCodeEnum.CLIPPER_ERROR.getCode(), e.getMessage());
        }
    }

    @Override
    public ClipperResponse<String> saveText(TextMessageRequest message, String userId) {
        // 参数校验
        if (message == null || StringUtils.isBlank(message.getContent())) {
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, "消息内容不能为空");
        }

        // 转换标签为数组格式
        final String[] tags = TagProcessor.convertToArray(message.getTags());

        // 使用工具类提取链接
        Optional<String> linkOpt = LinkExtractor.extractFirstLink(message.getContent());
        if (linkOpt.isPresent()) {
            // 尝试从文本中提取链接并异步处理
            handleLinkExtractionAsync(linkOpt.get(), tags, userId);
            UserConfig userConfig = configQueryApi.queryConfig(userId);
            if (userConfig.getSaveLinkText() == 1) {
                // 保存消息到核心服务
                String saveResult = clipperService.saveTextMessage(message.getContent(), tags, userId);
                if (saveResult.contains("失败")) {
                    return ClipperResponse.fail(ClipperErrCodeEnum.CLIPPER_ERROR.getCode(), saveResult);
                }
            }
        } else {
            // 保存消息到核心服务
            String saveResult = clipperService.saveTextMessage(message.getContent(), tags, userId);
            if (saveResult.contains("失败")) {
                return ClipperResponse.fail(ClipperErrCodeEnum.CLIPPER_ERROR.getCode(), saveResult);
            }
        }
        return ClipperResponse.success("保存成功");
    }

    /**
     * 构建ResolveFormRQ请求对象
     * 
     * @param result 解析结果
     * @param tags   标签数组
     * @return 构建的请求对象
     */
    private ResolveFormRQ buildResolveFormRQ(ResolveRS result, String[] tags) {
        ResolveFormRQ rq = new ResolveFormRQ(result);

        // 添加标签（使用工具类处理）
        if (!TagProcessor.isEmpty(tags)) {
            rq.setTags(TagProcessor.convertToList(tags));
        }

        return rq;
    }

    /**
     * 处理链接解析失败的情况
     * 
     * @param link   链接URL
     * @param tags   标签数组
     * @param userId 用户ID
     * @param e      异常信息
     */
    private void handleParseFailure(String link, String[] tags, String userId, Exception e) {
        // 构建错误信息的Block列表
        List<Block> errorBlocks = BlockUtils.buildErrorBlocks(link, e.getMessage());

        // 生成错误提示标题
        String errorTitle = ClipperConstants.ERROR_TITLE_PREFIX +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern(ClipperConstants.DATE_TIME_FORMAT));

        // 使用策略模式保存错误信息
        outputStrategyFactory.saveContent(userId, errorTitle, errorBlocks, tags, false);
    }

    /**
     * 异步处理链接提取和解析
     * 
     * @param content 消息内容
     * @param tags    标签数组
     * @param userId  用户ID
     */
    private void handleLinkExtractionAsync(String link, String[] tags, String userId) {
        // 异步解析链接
        EXECUTOR.submit(() -> {
            try {
                parseUrlThenSave(userId, tags, link);
            } catch (Exception e) {
                log.error("异步解析链接失败, link={}, userId={}", link, userId, e);
            }
        });

    }

}
