package com.maixi.road.clipper.strategy;

import java.util.List;

import org.springframework.stereotype.Component;

import com.maixi.road.clipper.constant.ClipperConstants;
import com.maixi.road.clipper.util.TagProcessor;
import com.maixi.road.common.business.wechat.enums.MsgTypeEnum;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.dto.UserConfig;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.markdown.dto.MarkdownSaveRQ;
import com.maixi.road.markdown.dto.MarkdownSaveRS;
import com.maixi.road.markdown.service.MarkdownOutputService;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * Markdown输出策略实现
 * 负责将内容保存为Markdown文件
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class MarkdownOutputStrategy implements OutputStrategy {

    @Resource
    private ConfigQueryApi configQueryApi;

    @Resource
    private MarkdownOutputService markdownOutputService;

    @Override
    public boolean saveContent(String userId, String title, List<Block> blocks, String[] tags, boolean isArticle) {
        try {
            // 获取用户配置
            UserConfig userConfig = configQueryApi.queryConfig(userId);
            String savePath = userConfig.getMarkdownSavePath();

            // 构建ResolveFormRQ对象
            ResolveFormRQ userForm = buildResolveFormRQ(title, blocks, tags, isArticle);

            // 构建MarkdownSaveRQ请求对象
            MarkdownSaveRQ request = MarkdownSaveRQ.builder()
                    .unionId(userId)
                    .userForm(userForm)
                    .savePath(savePath)
                    .fileName(title)
                    .createAssetsDir(false)
                    .downloadImages(false)
                    .includeMetadata(true)
                    .obConfig(userConfig.getObConfig())
                    .isArticle(isArticle)
                    .build();

            // 调用MarkdownOutputService保存文件
            Result<MarkdownSaveRS> result = markdownOutputService.saveArticle(request);

            if (result.isSuccess()) {
                log.info("成功保存内容到Markdown, userId={}, title={}, result={}", userId, title, result);
                return true;
            } else {
                log.error("保存内容到Markdown失败, userId={}, title={}, error={}", userId, title, result.getMsg());
                return false;
            }

        } catch (Exception e) {
            log.error("保存内容到Markdown异常, userId={}, title={}", userId, title, e);
            return false;
        }
    }

    @Override
    public String getStrategyType() {
        return ClipperConstants.OUTPUT_TYPE_MARKDOWN;
    }

    /**
     * 构建ResolveFormRQ对象
     * 
     * @param title     标题
     * @param blocks    内容块列表
     * @param tags      标签数组
     * @param isArticle 是否为文章
     * @return ResolveFormRQ对象
     */
    private ResolveFormRQ buildResolveFormRQ(String title, List<Block> blocks, String[] tags, boolean isArticle) {
        ResolveFormRQ userForm = new ResolveFormRQ();
        userForm.setTitle(title);
        userForm.setCategory(isArticle ? MsgTypeEnum.LINK.name() : MsgTypeEnum.TEXT.name());
        userForm.setBlocks(blocks);

        // 设置标签（如果有的话）
        if (!TagProcessor.isEmpty(tags)) {
            userForm.setTags(TagProcessor.convertToList(tags));
        }

        return userForm;
    }
}