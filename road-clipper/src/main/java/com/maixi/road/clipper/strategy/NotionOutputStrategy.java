package com.maixi.road.clipper.strategy;

import java.util.List;

import org.springframework.stereotype.Component;

import com.maixi.road.clipper.constant.ClipperConstants;
import com.maixi.road.clipper.output.helper.MessageBuildHelper;
import com.maixi.road.common.business.wechat.enums.MsgTypeEnum;
import com.maixi.road.common.core.model.dto.MessageFieldDTO;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.page.Page;
import com.maixi.road.common.integration.notion.model.page.Parent;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.notion.remote.NotionClient;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * Notion输出策略实现
 * 负责将内容保存到Notion数据库
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class NotionOutputStrategy implements OutputStrategy {

    @Resource
    private ConfigQueryApi configQueryApi;

    @Resource
    private NotionClient notionClient;

    @Override
    public boolean saveContent(String userId, String title, List<Block> blocks, String[] tags, boolean isArticle) {
        try {
            // 获取用户的Notion配置信息
            MessageFieldDTO userMessage = configQueryApi.getMessageFieldDTO(userId);

            // 确定消息类型
            MsgTypeEnum msgType = isArticle ? MsgTypeEnum.LINK : MsgTypeEnum.TEXT;

            // 构建Notion页面
            Page page = Page.builder()
                    .parent(Parent.build(userMessage.getDatabaseId()))
                    .properties(MessageBuildHelper.propertiesBuild(userMessage, title, msgType, tags))
                    .children(blocks)
                    .build();

            // 保存到Notion
            notionClient.createPageWithRetry(userMessage.getAccessToken(), page, userId);

            log.info("成功保存内容到Notion, userId={}, title={}", userId, title);
            return true;

        } catch (InterruptedException e) {
            log.error("保存内容到Notion被中断, userId={}, title={}", userId, title, e);
            Thread.currentThread().interrupt();
            return false;
        } catch (Exception e) {
            log.error("保存内容到Notion失败, userId={}, title={}", userId, title, e);
            return false;
        }
    }

    @Override
    public String getStrategyType() {
        return ClipperConstants.OUTPUT_TYPE_NOTION;
    }
}