package com.maixi.road.clipper.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 标签处理工具类
 * 
 * <AUTHOR>
 */
public class TagProcessor {

    /**
     * 私有构造函数，防止实例化
     */
    private TagProcessor() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * 将标签列表转换为字符串数组
     * 
     * @param tags 标签列表
     * @return 字符串数组，如果输入为null或空则返回空数组
     */
    public static String[] convertToArray(List<String> tags) {
        return Optional.ofNullable(tags)
                .filter(list -> !list.isEmpty())
                .map(list -> list.toArray(new String[0]))
                .orElse(new String[0]);
    }

    /**
     * 将字符串数组转换为标签列表
     * 
     * @param tags 字符串数组
     * @return 标签列表，如果输入为null或空则返回空列表
     */
    public static List<String> convertToList(String[] tags) {
        return Optional.ofNullable(tags)
                .filter(array -> array.length > 0)
                .map(Arrays::asList)
                .map(ArrayList::new)
                .orElse(new ArrayList<>());
    }

    /**
     * 安全地将标签数组添加到标签列表中
     * 
     * @param targetList 目标列表
     * @param tags       要添加的标签数组
     * @return 添加后的列表
     */
    public static List<String> addTagsToList(List<String> targetList, String[] tags) {
        List<String> result = Optional.ofNullable(targetList)
                .map(ArrayList::new)
                .orElse(new ArrayList<>());

        if (tags != null && tags.length > 0) {
            result.addAll(Arrays.asList(tags));
        }

        return result;
    }

    /**
     * 检查标签数组是否为空
     * 
     * @param tags 标签数组
     * @return 如果为空或null返回true，否则返回false
     */
    public static boolean isEmpty(String[] tags) {
        return tags == null || tags.length == 0;
    }

    /**
     * 检查标签列表是否为空
     * 
     * @param tags 标签列表
     * @return 如果为空或null返回true，否则返回false
     */
    public static boolean isEmpty(List<String> tags) {
        return tags == null || tags.isEmpty();
    }
}