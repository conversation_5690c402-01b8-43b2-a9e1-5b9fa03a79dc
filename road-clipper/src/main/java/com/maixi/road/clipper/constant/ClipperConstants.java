package com.maixi.road.clipper.constant;

/**
 * 剪藏模块常量类
 * 
 * <AUTHOR>
 */
public class ClipperConstants {

    /**
     * 输出类型常量
     */
    public static final String OUTPUT_TYPE_NOTION = "notion";
    public static final String OUTPUT_TYPE_MARKDOWN = "markdown";
    public static final String OUTPUT_TYPE_BOTH = "both";

    /**
     * 时间格式化模式
     */
    public static final String DATE_TIME_FORMAT = "yyyyMMdd_HHmmss";

    /**
     * 错误提示标题前缀
     */
    public static final String ERROR_TITLE_PREFIX = "温馨提示-";

    /**
     * URL解析延迟时间（毫秒）
     * TODO: 需要调查这个延迟的必要性
     */
    public static final long PARSE_DELAY_MS = 10000L;

    /**
     * URL匹配正则表达式
     */
    public static final String URL_REGEX = "(https?://[^\\s]+)";

    /**
     * 私有构造函数，防止实例化
     */
    private ClipperConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}