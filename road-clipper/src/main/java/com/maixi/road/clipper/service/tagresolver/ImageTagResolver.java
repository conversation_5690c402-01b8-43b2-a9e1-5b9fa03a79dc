package com.maixi.road.clipper.service.tagresolver;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.service.tagresolver.weixin.MpImgTagResolver;
import com.maixi.road.clipper.util.CSSStyleParser;
import com.maixi.road.common.core.utils.ImageUtil;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Image;

import lombok.extern.slf4j.Slf4j;

/**
 * 图片标签解析器，处理 <img> 标签
 */
@Component("imageTagResolver")
@Slf4j
public class ImageTagResolver implements ContentTagResolver {

    /**
     * 小图片过滤的最小宽度阈值（像素）
     */
    private static final double MIN_IMAGE_WIDTH = 50.0;
    /**
     * 判断是否为图片标签
     * @param node DOM 元素
     * @return 是否支持
     */
    @Override
    public boolean supports(Node node) {
        return "img".equalsIgnoreCase(node.nodeName());
    }

    /**
     * 解析图片标签为 Block
     * @param node DOM 元素
     * @return Block 结构
     */
    @Override
    public List<Block> resolve(Node node) {
        Element element = (Element) node;
        String src = element.attr("src");
        if (src.contains("we-emoji")) {
            return Lists.newArrayList();
        }

        // 过滤 135编辑器 的修饰图片
        String w = element.attr("data-w");
        if (StringUtils.isNotBlank(w) && Integer.parseInt(w) < 50) {
            return null;
        }

        // 检查CSS样式中的尺寸，过滤小图片
        if (isSmallImageByStyle(element)) {
            log.debug("过滤小图片: src={}, style={}", src, element.attr("style"));
            return Lists.newArrayList();
        }

        String dataSrc = element.attr("data-src");
        if (dataSrc.startsWith("http")) {
            src = dataSrc;
        }
        if (StringUtils.isNotBlank(src)) {
            if (src.contains("we-emoji")){
                return Lists.newArrayList();
            }
            if (src.startsWith("https://mmbiz.qpic.cn/")) {
                String imgType = MpImgTagResolver.analyzerImageType(src);
                String imageSrc = MpImgTagResolver.buildCorrectImageSrc(src, imgType);
                return Lists.newArrayList(Block.image(new Image(ImageUtil.transform2SecureUrl(imageSrc))));
            }
            String[] split = src.split("\\?");
            String imageSrc = split[0];
            if (imageSrc.startsWith("awebp")) {
                imageSrc = imageSrc.replace("awebp", "webp");
            }
            return Lists.newArrayList(Block.image(new Image(imageSrc)));
        }
        return Lists.newArrayList();
    }

    /**
     * 检查是否为小图片（基于CSS样式）
     *
     * @param element 图片元素
     * @return true表示是小图片，应该被过滤
     */
    private boolean isSmallImageByStyle(Element element) {
        // 1. 检查内联样式中的尺寸
        String style = element.attr("style");
        if (StringUtils.isNotBlank(style)) {
            if (CSSStyleParser.isSmallImageByWidth(style, MIN_IMAGE_WIDTH)) {
                return true;
            }
        }

        // 2. 检查HTML属性中的尺寸
        String width = element.attr("width");
        String height = element.attr("height");

        if (StringUtils.isNotBlank(width)) {
            try {
                double widthValue = Double.parseDouble(width);
                if (widthValue < MIN_IMAGE_WIDTH) {
                    log.debug("图片width属性{}px小于阈值{}px，将被过滤", widthValue, MIN_IMAGE_WIDTH);
                    return true;
                }
            } catch (NumberFormatException e) {
                log.debug("无法解析width属性: {}", width);
            }
        }

        if (StringUtils.isNotBlank(height)) {
            try {
                double heightValue = Double.parseDouble(height);
                if (heightValue < MIN_IMAGE_WIDTH) {
                    log.debug("图片height属性{}px小于阈值{}px，将被过滤", heightValue, MIN_IMAGE_WIDTH);
                    return true;
                }
            } catch (NumberFormatException e) {
                log.debug("无法解析height属性: {}", height);
            }
        }

        return false;
    }
}
