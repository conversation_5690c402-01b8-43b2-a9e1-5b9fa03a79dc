package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Bookmark;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 抖音解析器
 * 支持解析抖音视频页面内容
 */
@Slf4j
@Component
public class DouyinParser extends AbstractParser {

    @Resource
    private ConfigQueryApi configQueryApi;

    @Override
    public boolean supports(String url) {
        return url != null && url.contains("douyin.com");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        // 抖音视频页面解析，暂时返回书签
        Document document = getDocument(url);
        return Lists.newArrayList(Block.buildBookmark(new Bookmark(url, parseTitle(document))));
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.DOUYIN.getName();
    }

    @Override
    protected String parsePublishTime(Document document) {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    @Override
    protected String parseAuthor(Document document) {
        return "抖音用户";
    }

    @Override
    protected String parseTitle(Document document) {
        try {
            // 优先从视频标题获取
            Elements titleElements = document.select("[data-e2e=video-title], .video-title, .title");
            if (!titleElements.isEmpty()) {
                return titleElements.text().trim();
            }

            // 从meta标签获取
            Elements metaElements = document.select("meta[property=og:title], meta[name=twitter:title]");
            if (!metaElements.isEmpty()) {
                return metaElements.attr("content").trim();
            }

            // 从h1标签获取
            Elements h1Elements = document.select("h1");
            if (!h1Elements.isEmpty()) {
                return h1Elements.text().trim();
            }

            // 从页面标题获取
            String pageTitle = document.title();
            if (StringUtils.isNotBlank(pageTitle)) {
                return pageTitle.trim();
            }
        } catch (Exception e) {
            log.warn("解析抖音标题失败", e);
        }
        return "抖音视频";
    }

    @Override
    protected String parseCover(Document document) {
        return defaultHeadImgUrl();
    }

    @Override
    protected String parseDescription(Document document) {
        try {
            // 从视频描述获取
            Elements descElements = document.select("[data-e2e=video-desc], .video-desc, .description");
            if (!descElements.isEmpty()) {
                return descElements.text().trim();
            }

            // 从meta标签获取
            Elements metaElements = document.select("meta[property=og:description], meta[name=description]");
            if (!metaElements.isEmpty()) {
                return metaElements.attr("content").trim();
            }
        } catch (Exception e) {
            log.warn("解析抖音描述失败", e);
        }
        return "";
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_DOUYIN;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_DOUYIN;
    }

    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }
}