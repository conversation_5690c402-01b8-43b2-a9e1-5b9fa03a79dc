package com.maixi.road.clipper.service.tagresolver;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Heading;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.common.RichText;

import lombok.extern.slf4j.Slf4j;

/**
 * 图片标签解析器，处理 <img> 标签
 */
@Slf4j
@Component
public class HeadingTagResolver implements ContentTagResolver {

    private static final List<String> headingTags = List.of("h1", "h2", "h3", "h4", "h5", "h6");

    private static final byte[] bytes = {(byte) 0xC2, (byte) 0xA0};

    public static final String UTFSpace = new String(bytes, StandardCharsets.UTF_8);

    /**
     * 判断是否为图片标签
     * @param node DOM 元素
     * @return 是否支持
     */
    @Override
    public boolean supports(Node node) {
        return headingTags.contains(node.nodeName());
    }

    /**
     * 解析图片标签为 Block
     * @param node DOM 元素
     * @return Block 结构
     */
    @Override
    public List<Block> resolve(Node node) {
        Element element = (Element) node;
        // 如果含有 a 标签的子元素，并且 element 内部的 text 与 a 标签的 text 一致，则只解析 a 标签
        Elements aElement = element.getElementsByTag("a");
        if (!aElement.isEmpty() && element.text().equals(aElement.text())) {
            return Lists.newArrayList(Block.paragraph(RichTexts.build(
                    Collections.singletonList(RichText.textWithBlueLink(element.text(), aElement.attr("href"))))));
        }
        String content = element.text().replaceAll(UTFSpace, " ");
        if (StringUtils.isBlank(content)) {
            // 异常告警即可
            log.warn("解析到{}标签，但是内容为空. element.text={}",element.nodeName(), element.wholeText());
        }
        if ("h1".equalsIgnoreCase(element.nodeName())) {
            return Lists.newArrayList(Block.heading_1(Heading.buildHeading(content)));
        }
        if ("h2".equalsIgnoreCase(element.nodeName())) {
            return Lists.newArrayList(Block.heading_2(Heading.buildHeading(content)));
        }
        if ("h3".equalsIgnoreCase(element.nodeName())) {
            return Lists.newArrayList(Block.heading_3(Heading.buildHeading(content)));
        }
        // 其他的h4、h5、h6都当做普通段落处理
        return Lists.newArrayList(Block.paragraph(RichTexts.build(Collections.singletonList(RichText.boldText(content)))));
    }
}
