package com.maixi.road.clipper.strategy;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.maixi.road.clipper.constant.ClipperConstants;
import com.maixi.road.common.core.model.dto.UserConfig;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 输出策略工厂类
 * 负责根据用户配置选择合适的输出策略
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class OutputStrategyFactory {

    @Resource
    private List<OutputStrategy> outputStrategies;

    @Resource
    private ConfigQueryApi configQueryApi;

    /**
     * 策略映射表，key为策略类型，value为策略实现
     */
    private Map<String, OutputStrategy> strategyMap;

    /**
     * 初始化策略映射表
     */
    @PostConstruct
    public void initStrategyMap() {
        strategyMap = outputStrategies.stream()
                .collect(Collectors.toMap(
                        OutputStrategy::getStrategyType,
                        Function.identity()));

        log.info("初始化输出策略映射表完成, 可用策略: {}", strategyMap.keySet());
    }

    /**
     * 根据用户配置保存内容
     * 支持单一策略和多策略（both模式）
     * 
     * @param userId    用户ID
     * @param title     标题
     * @param blocks    内容块列表
     * @param tags      标签数组
     * @param isArticle 是否为文章
     * @return 保存是否成功
     */
    public boolean saveContent(String userId, String title, List<Block> blocks, String[] tags, boolean isArticle) {
        try {
            // 获取用户配置
            UserConfig userConfig = configQueryApi.queryConfig(userId);
            String outputType = userConfig.getOutputType();

            // 如果输出类型为空，默认使用Notion
            if (StringUtils.isBlank(outputType)) {
                outputType = ClipperConstants.OUTPUT_TYPE_NOTION;
                log.info("用户未配置输出类型，默认使用Notion, userId={}", userId);
            }

            return executeOutputStrategy(outputType, userId, title, blocks, tags, isArticle);

        } catch (Exception e) {
            log.error("保存内容失败, userId={}, title={}", userId, title, e);
            return false;
        }
    }

    /**
     * 执行输出策略
     * 
     * @param outputType 输出类型
     * @param userId     用户ID
     * @param title      标题
     * @param blocks     内容块列表
     * @param tags       标签数组
     * @param isArticle  是否为文章
     * @return 保存是否成功
     */
    private boolean executeOutputStrategy(String outputType, String userId, String title,
            List<Block> blocks, String[] tags, boolean isArticle) {

        // 处理both模式：同时保存到Notion和Markdown
        if (ClipperConstants.OUTPUT_TYPE_BOTH.equalsIgnoreCase(outputType)) {
            return executeBothStrategy(userId, title, blocks, tags, isArticle);
        }

        // 处理单一策略模式
        return executeSingleStrategy(outputType, userId, title, blocks, tags, isArticle);
    }

    /**
     * 执行both模式：同时保存到Notion和Markdown
     * 
     * @param userId    用户ID
     * @param title     标题
     * @param blocks    内容块列表
     * @param tags      标签数组
     * @param isArticle 是否为文章
     * @return 保存是否成功（只要有一个成功就返回true）
     */
    private boolean executeBothStrategy(String userId, String title, List<Block> blocks, String[] tags,
            boolean isArticle) {
        boolean notionSuccess = executeSingleStrategy(ClipperConstants.OUTPUT_TYPE_NOTION, userId, title, blocks, tags,
                isArticle);
        boolean markdownSuccess = executeSingleStrategy(ClipperConstants.OUTPUT_TYPE_MARKDOWN, userId, title, blocks,
                tags, isArticle);

        if (notionSuccess && markdownSuccess) {
            log.info("同时保存到Notion和Markdown成功, userId={}, title={}", userId, title);
            return true;
        } else if (notionSuccess || markdownSuccess) {
            log.warn("部分保存成功, userId={}, title={}, notion={}, markdown={}",
                    userId, title, notionSuccess, markdownSuccess);
            return true;
        } else {
            log.error("保存到Notion和Markdown都失败, userId={}, title={}", userId, title);
            return false;
        }
    }

    /**
     * 执行单一策略
     * 
     * @param outputType 输出类型
     * @param userId     用户ID
     * @param title      标题
     * @param blocks     内容块列表
     * @param tags       标签数组
     * @param isArticle  是否为文章
     * @return 保存是否成功
     */
    private boolean executeSingleStrategy(String outputType, String userId, String title,
            List<Block> blocks, String[] tags, boolean isArticle) {

        OutputStrategy strategy = strategyMap.get(outputType.toLowerCase());
        if (strategy == null) {
            log.error("未找到对应的输出策略, outputType={}, userId={}", outputType, userId);
            return false;
        }

        return strategy.saveContent(userId, title, blocks, tags, isArticle);
    }
}