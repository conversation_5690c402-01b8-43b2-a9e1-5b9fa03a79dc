package com.maixi.road.clipper.service.parser.factory;

import java.util.Map;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.maixi.road.clipper.service.parser.ContentParser;
import com.maixi.road.clipper.service.parser.impl.general.UniversalParser;
import com.maixi.road.common.core.enums.error.ClipperErrCodeEnum;
import com.maixi.road.common.core.exception.ClipperException;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 解析器工厂
 */
@Slf4j
@Component
public class ParserFactory {

    @Resource
    private Map<String, ContentParser> parsers;
    @Resource
    private UniversalParser universalParser;

    /**
     * 根据URL获取合适的解析器
     * 
     * @param url 要解析的URL
     * @return 内容解析器
     */
    public ContentParser getParser(String url) {
        // 查找支持该URL的解析器
        ContentParser parser = parsers.values().stream()
                .filter(e -> !(e instanceof UniversalParser))
                .filter(p -> p.supports(url))
                .findFirst()
                .orElse(null);

        ContentParser finalParser = Optional.ofNullable(parser).orElse(universalParser);
        return finalParser;
    }

    /**
     * 规范化URL
     * 
     * @param url 原始URL
     * @return 规范化后的URL
     */
    private String normalizeUrl(String originalUrl) {

        String url = originalUrl;
        // 确保URL以http://或https://开头
        if (!originalUrl.startsWith("http://") && !originalUrl.startsWith("https://")) {
            url = "https://" + originalUrl;
            log.info("URL不以http://或https://开头，自动添加https://, \n originalUrl: {}, \n url: {}", originalUrl, url);
        }

        return url;
    }
}
