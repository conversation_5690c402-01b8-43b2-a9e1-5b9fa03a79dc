package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.InlineElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Callout;
import com.maixi.road.common.integration.notion.model.block.Heading;
import com.maixi.road.common.integration.notion.model.block.Image;
import com.maixi.road.common.integration.notion.model.block.ListItem;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class RedNoteParser extends AbstractParser {

    private static final Pattern timePattern = Pattern.compile(".*?(\\d{4}-\\d{2}-\\d{2}).*?");
    private static final Pattern currentYearPattern = Pattern.compile("(\\d{2}-\\d{2})");
    private static final Pattern fewDays = Pattern.compile("(\\d{1,2}) 天前");
    private static final Pattern today = Pattern.compile("今天");
    private static final Pattern yesterday = Pattern.compile("昨天");
    private static final Pattern theDayBeforeYesterday = Pattern.compile("前天");

    @Resource
    private ConfigQueryApi configQueryApi;
    @Resource
    private InlineElementParser inlineElementParser;
    @Resource
    private GlobalElementParser globalElementParser;

    @Override
    public boolean supports(String url) {
        return url.contains("xiaohongshu.com") || url.contains("xhslink.com");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        Document document = getDocument(url);

        List<Block> blocks = Lists.newArrayList();
        // 解析正文内容
        Element element = document.getElementById("detail-desc");
        if (element == null) {
            String text = document.body().text().replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE);
            blocks.add(Block.paragraph(RichTexts.build(Collections.singletonList(RichText.simpleText(text)))));
            blocks.add(Block.paragraph(RichTexts.EMPTY));
            return blocks;
        }
        
        // 如果配置了toObsidian，则剔除id=hash-tag的元素
        if (configQueryApi.queryConfig(userId).getToObsidian() == 1) {
            // 剔除id=hash-tag的元素
            element.select("a[id=\"hash-tag\"]").remove();
        }

        Elements spans = element.children();
        if (spans.isEmpty()) {
            String text = element.text().replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE);
            blocks.add(Block.paragraph(RichTexts.build(Collections.singletonList(RichText.simpleText(text)))));
            blocks.add(Block.paragraph(RichTexts.EMPTY));
        } else {
            List<Block> blockList = Lists.newArrayList();
            for (Element span : spans) {
                blockList.addAll(inlineElementParser.parseElement(span));
            }
            blocks.addAll(blockList);
            blocks.add(Block.paragraph(RichTexts.EMPTY));
        }

        String type = getType(document);
        if ("article".equals(type)) {
            blocks.add(Block.heading_3(Heading.buildHeading("文章中收集到的图片链接")));
            Elements elements = document.select("meta[name=\"og:image\"]");
            S3Config imgS3Config = configQueryApi.getImgS3Config(userId);
            if (imgS3Config == null) {
                if (!elements.isEmpty()) {
                    RichText richText1 = RichText.simpleRedText("请务必及时保存图片，小红书图片链接可能过期失效\n");
                    RichText richText2 = RichText.simpleRedText("操作方式：①点击链接 ②复制为图片 ③并插入到本页面合适位置 ④Cover图片建议也替换掉");
                    blocks.add(Block.quote(RichTexts.redBuild(Lists.newArrayList(richText1, richText2))));
                }
                for (Element item : elements) {
                    String imgUrl = item.attr("content");
                    blocks.add(Block.buildNumberedListItem(
                            ListItem.buildItem(RichText.textWithLink(imgUrl, imgUrl))));
                }
            } else {
                int start = 1;
                for (Element item : elements) {
                    String imgUrl = item.attr("content");
                    blocks.add(Block.image(new Image(imgUrl, "第" + start++ + "张图片")));
                }
            }
        } else if ("video".equals(type)) {
            blocks.addFirst(Block.callout(Callout.buildTip("视频链接有时效性，请尽快点击链接下载保存")));
            blocks.add(Block.heading_3(Heading.buildHeading("文章中收集到的视频链接")));
            Elements elements = document.select("meta[name=\"og:video\"]");
            for (Element item : elements) {
                String videoUrl = item.attr("content");
                blocks.add(Block.buildNumberedListItem(
                        ListItem.buildItem(RichText.textWithLink(videoUrl, videoUrl))));
            }
        }
        return blocks;

    }

    private String getType(Document document) {
        Elements elements = document.select("meta[name=\"og:type\"]");
        return Optional.ofNullable(elements.first()).map(e -> e.attr("content")).orElse(null);
    }

    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.RB.getName();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_XHS;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_XHS;
    }

    @Override
    protected String parseAuthor(Document document) {
        Elements elements = document.select("span[class=\"username\"]");
        String authorName = Optional.ofNullable(elements.first()).map(Element::text).orElse(null);
        if (authorName != null) {
            return authorName;
        }
        log.error("解析文章作者失败,url={}", document.location());
        return "未知";
    }

    @Override
    protected String parseTitle(Document document) {
        Elements title_elements = document.getElementsByTag("title");
        String title = Optional.ofNullable(title_elements.first()).map(Element::text).orElse(null);
        if (StringUtils.isNoneBlank(title)) {
            return title;
        }
        Elements og_title_elements = document.select("meta[name=\"og:title\"]");
        title = Optional.ofNullable(og_title_elements.first()).map(e -> e.attr("content")).orElse(null);
        if (title == null) {
            log.error("解析文章标题失败,url={}", document.location());
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "标题内容解析失败");
        }
        return title;
    }

    @Override
    protected String parsePublishTime(Document document) {
        Elements elements = document.select("div[class=\"bottom-container\"]");
        for (Element element : elements) {
            Elements spans = element.getElementsByClass("date");
            for (Element span : spans) {
                Matcher matcher = timePattern.matcher(span.text());
                // 编辑于 今天 17:21 浙江
                // 7 天前 浙江
                // 6 天前 湖南
                // 03-10
                // 2023-10-18
                if (matcher.find()) {
                    String time = matcher.group(1);
                    try {
                        return LocalDate.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay()
                                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    } catch (Exception e) {
                        log.error("时间格式解析失败,url={},time={}", document.location(), element.text());
                    }
                }
                Matcher currentYearMatcher = currentYearPattern.matcher(span.text());
                if (currentYearMatcher.find()) {
                    String time = currentYearMatcher.group(1);
                    try {
                        return LocalDate
                                .parse(LocalDate.now().getYear() + "-" + time,
                                        DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                                .atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    } catch (Exception e) {
                        log.error("时间格式解析失败,url={},time={}", document.location(), element.text());
                    }
                }
                Matcher fewDaysMatcher = fewDays.matcher(span.text());
                if (fewDaysMatcher.find()) {
                    String time = fewDaysMatcher.group(1);
                    try {
                        return LocalDateTime.now().minusDays(Integer.parseInt(time))
                                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    } catch (Exception e) {
                        log.error("时间格式解析失败,url={},time={}", document.location(), element.text());
                    }
                }
                Matcher todayMatcher = today.matcher(span.text());
                if (todayMatcher.find()) {
                    return LocalDate.now().atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }
                Matcher yesterdayMatcher = yesterday.matcher(span.text());
                if (yesterdayMatcher.find()) {
                    return LocalDate.now().minusDays(1).atStartOfDay()
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }
                Matcher theDayBeforeYesterdayMatcher = theDayBeforeYesterday.matcher(span.text());
                if (theDayBeforeYesterdayMatcher.find()) {
                    return LocalDate.now().minusDays(2).atStartOfDay()
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }
            }
        }
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

}
