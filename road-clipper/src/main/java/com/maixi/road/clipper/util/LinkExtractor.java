package com.maixi.road.clipper.util;

import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.maixi.road.clipper.constant.ClipperConstants;

/**
 * 链接提取工具类
 * 
 * <AUTHOR>
 */
public class LinkExtractor {

    /**
     * URL匹配正则表达式编译后的Pattern对象
     */
    private static final Pattern URL_PATTERN = Pattern.compile(ClipperConstants.URL_REGEX);

    /**
     * 私有构造函数，防止实例化
     */
    private LinkExtractor() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * 从文本中提取第一个链接
     * 
     * @param text 待提取的文本
     * @return 提取到的链接，如果没有找到则返回Optional.empty()
     */
    public static Optional<String> extractFirstLink(String text) {
        if (StringUtils.isBlank(text)) {
            return Optional.empty();
        }

        Matcher matcher = URL_PATTERN.matcher(text);
        if (matcher.find()) {
            return Optional.of(matcher.group(1));
        }

        return Optional.empty();
    }

    /**
     * 检查文本中是否包含链接
     * 
     * @param text 待检查的文本
     * @return 如果包含链接返回true，否则返回false
     */
    public static boolean containsLink(String text) {
        return extractFirstLink(text).isPresent();
    }
}