package com.maixi.road.clipper.strategy;

import java.util.List;

import com.maixi.road.common.integration.notion.model.block.Block;

/**
 * 输出策略接口
 * 用于定义不同的内容输出方式
 * 
 * <AUTHOR>
 */
public interface OutputStrategy {

    /**
     * 保存内容到指定的输出目标
     * 
     * @param userId    用户ID
     * @param title     标题
     * @param blocks    内容块列表
     * @param tags      标签数组
     * @param isArticle 是否为文章类型
     * @return 保存是否成功
     */
    boolean saveContent(String userId, String title, List<Block> blocks, String[] tags, boolean isArticle);

    /**
     * 获取策略类型标识
     * 
     * @return 策略类型字符串
     */
    String getStrategyType();
}