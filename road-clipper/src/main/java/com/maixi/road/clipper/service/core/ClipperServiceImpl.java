package com.maixi.road.clipper.service.core;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.maixi.road.clipper.image.ImageProcessService;
import com.maixi.road.clipper.image.ImageUploadHelper;
import com.maixi.road.clipper.output.NotionOutputService;
import com.maixi.road.clipper.output.helper.MessageBuildHelper;
import com.maixi.road.clipper.service.parser.ContentParser;
import com.maixi.road.clipper.service.parser.factory.ParserFactory;
import com.maixi.road.cloudfunc.notion.dto.rs.PageCreateRS;
import com.maixi.road.common.business.wechat.enums.MsgTypeEnum;
import com.maixi.road.common.core.enums.ThreadLocalKeys;
import com.maixi.road.common.core.enums.error.ClipperErrCodeEnum;
import com.maixi.road.common.core.exception.ClipperException;
import com.maixi.road.common.core.model.dto.MessageFieldDTO;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.core.model.dto.ResolveRS;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.dto.UserConfig;
import com.maixi.road.common.core.utils.BlockUtils;
import com.maixi.road.common.core.utils.ThreadLocalUtils;
import com.maixi.road.common.core.utils.UrlUtils;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.page.Page;
import com.maixi.road.common.integration.notion.model.page.Parent;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.common.service.redis.api.RedisOpApi;
import com.maixi.road.markdown.dto.MarkdownSaveRQ;
import com.maixi.road.markdown.dto.MarkdownSaveRS;
import com.maixi.road.markdown.service.MarkdownOutputService;
import com.maixi.road.notion.remote.NotionClient;
import com.maixi.road.notion.remote.dto.request.PageCreateRQ;
import com.maixi.road.notion.remote.manager.ParagraphBlockBuilder;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 剪藏服务实现
 */
@Slf4j
@Service
public class ClipperServiceImpl implements ClipperCoreService {

    @Resource
    private ParserFactory parserFactory;
    @Resource
    private ImageProcessService imageProcessService;
    @Resource
    private NotionOutputService notionOutputService;
    @Resource
    private MarkdownOutputService markdownOutputService;
    @Resource
    private ConfigQueryApi configQueryApi;
    @Resource
    private RedisOpApi redisOpApi;
    @Resource
    private NotionClient notionClient;

    // 用于存储异步正文解析任务的Future，使用虚拟线程提高I/O密集型任务性能
    private final ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();

    /**
     * 解析URL，只同步返回basicResult，正文和正文图片异步处理
     *
     * @param url    文章URL
     * @param userId 用户ID
     * @return 文章基础属性
     */
    @Override
    public ResolveRS parseUrl(String url, String userId) {
        log.info("开始解析URL, url={}, userId={}", url, userId);
        String normalizedUrl = UrlUtils.normalizeUrl(url);
        log.info("规范化URL, normalizedUrl={}", normalizedUrl);
        try {
            // 获取合适的解析器
            ContentParser parser = parserFactory.getParser(normalizedUrl);
            log.info("解析器, url: {}, parser: {}", normalizedUrl, parser.getClass().getSimpleName());

            // 只解析基本信息
            long t0 = System.currentTimeMillis();
            ResolveRS basicResult = parser.parseBasicInfo(normalizedUrl, userId);
            long t1 = System.currentTimeMillis();
            log.info("解析基本信息耗时, url={}, cost={}s", normalizedUrl, (t1 - t0) / 1000);
            log.info("解析结果, url={}, result={}", normalizedUrl, JSON.toJSONString(basicResult));

            // 异步解析正文和正文图片
            executor.execute(() -> {
                try {
                    // 使用TransmittableThreadLocal存储解析结果，支持线程间传递
                    ThreadLocalUtils.set(ThreadLocalKeys.CLIPPER_ORIGIN.getKey(), basicResult.getOrigin());
                    // 解析正文
                    long t3 = System.currentTimeMillis();
                    basicResult.setBlocks(parser.parseContent(normalizedUrl, userId));
                    long t4 = System.currentTimeMillis();
                    log.info("解析正文耗时, url={}, cost={}s", normalizedUrl, (t4 - t3) / 1000);
                    // 处理图片
                    processImages(basicResult, userId);
                    long t5 = System.currentTimeMillis();
                    log.info("处理图片耗时, url={}, cost={}s", normalizedUrl, (t5 - t4) / 1000);
                    // 保存解析结果到Redis
                    redisOpApi.saveClipResult(basicResult.getLink(), basicResult);
                } catch (Exception e) {
                    log.error("解析正文异常, url={}, userId={}", normalizedUrl, userId, e);
                } finally {
                    // 清理ThreadLocal，防止内存泄漏
                    ThreadLocalUtils.remove(ThreadLocalKeys.CLIPPER_ORIGIN.getKey());
                }
            });
            return basicResult;
        } catch (Exception e) {
            log.error("URL解析异常, url={}, userId={}", normalizedUrl, userId, e);
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, e.getMessage());
        }
    }

    /**
     * 提交文章时，阻塞等待正文解析结果，超时10秒
     *
     * @param article 文章参数
     * @param userId  用户ID
     * @return 是否成功
     */
    @Override
    public boolean saveContent(ResolveFormRQ article, String userId) {
        log.info("开始提交文章, title={}, link={}, userId={}", article.getTitle(), article.getLink(), userId);
        // 阻塞等待正文内容，依赖内部超时机制（15秒）
        waitForContent(article, userId);

        // 获取用户配置,确定输出类型
        UserConfig userConfig = configQueryApi.queryConfig(userId);

        if ("markdown".equalsIgnoreCase(userConfig.getOutputType())) {
            // 输出到 Markdown
            return save2markdown(article, userId, userConfig);
        } else {
            // 默认输出到 Notion
            return save2notion(article, userId, userConfig);
        }

    }

    /**
     * 保存文本消息到Notion和Markdown（根据用户配置）
     * 
     * @param content 消息内容
     * @param tags    标签数组
     * @param userId  用户ID
     * @return 保存结果
     */
    @Override
    public String saveTextMessage(String content, String[] tags, String userId) {
        try {
            // 获取用户配置
            UserConfig userConfig = configQueryApi.queryConfig(userId);

            boolean save2markdown = false;
            if ("markdown".equalsIgnoreCase(userConfig.getOutputType())) {
                save2markdown = true;
            }

            // 保存到Markdown
            if (save2markdown) {
                log.info("保存消息到Markdown, content={}, userId={}", content, userId);
                boolean result = saveMessageToMarkdown(content, tags, userId, userConfig);
                if (result) {
                    return "保存成功";
                }
            } else {
                log.info("保存消息到Notion, content={}, userId={}", content, userId);
                // 获取消息字段配置
                MessageFieldDTO messageFieldDTO = configQueryApi.getMessageFieldDTO(userId);
                // 构建Notion页面
                Page page = buildMessageNotionPage(messageFieldDTO, content, tags);
                boolean result = saveMessageToNotion(messageFieldDTO, page, userId);
                if (result) {
                    return "保存成功";
                }
            }
            return "保存失败";
        } catch (Exception e) {
            log.error("保存消息异常, content={}, userId={}", content.substring(0, Math.min(content.length(), 20)), userId, e);
            return "保存失败: " + e.getMessage();
        }
    }

    private boolean save2notion(ResolveFormRQ article, String userId, UserConfig userConfig) {
        PageCreateRQ createRequest = PageCreateRQ.builder()
                .unionId(userId)
                .userForm(article)
                .blockSizeLimit(configQueryApi.gloablConfig().getBlockSizeLimit())
                .noCover(userConfig.getNoCover() == 1)
                .noIcon(userConfig.getNoIcon() == 1)
                .build();
        Result<PageCreateRS> createResult = notionOutputService.saveArticle(createRequest);
        if (createResult.isSuccess()) {
            log.info("文章已保存到 Notion, title={}, link={}", article.getTitle(), article.getLink());
            return true;
        }
        throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, createResult.getMsg());
    }

    private boolean save2markdown(ResolveFormRQ article, String userId, UserConfig userConfig) {
        MarkdownSaveRQ markdownRequest = MarkdownSaveRQ.builder()
                .unionId(userId)
                .userForm(article)
                .savePath(userConfig.getMarkdownSavePath())
                .createAssetsDir(true)
                .downloadImages(true)
                .includeMetadata(true)
                .obConfig(userConfig.getObConfig())
                .isArticle(true)
                .build();
        Result<MarkdownSaveRS> markdownResult = markdownOutputService.saveArticle(markdownRequest);
        if (markdownResult.isSuccess()) {
            log.info("文章已保存为 Markdown, title={}, link={}, s3Url={}", article.getTitle(), article.getLink(),
                    markdownResult.getData().getS3Url());
            return true;
        }
        throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, markdownResult.getMsg());
    }

    private void waitForContent(ResolveFormRQ article, String userId) {
        long t0 = System.currentTimeMillis();
        Future<ResolveRS> future = redisOpApi.getClipResult(article.getLink());
        try {
            // 内部异步任务会在15秒后返回空列表（如果未获取到数据）
            ResolveRS resolveRS = future.get();
            if (resolveRS != null) {
                article.setBlocks(resolveRS.getBlocks());
                if (StringUtils.isNotBlank(resolveRS.getCover())) {
                    article.setCover(resolveRS.getCover());
                }
                log.info("获取正文解析结果成功, link={}, blocks数量={}, time={}ms",
                        article.getLink(), resolveRS.getBlocks().size(), System.currentTimeMillis() - t0);
            } else {
                log.warn("获取正文解析结果为空,link={}, time={}s", article.getLink(), (System.currentTimeMillis() - t0) / 1000);
                List<Block> tipBlocks = BlockUtils.buildErrorBlocks(article.getLink(), "正文解析失败，兜底预留网页书签");
                article.setBlocks(tipBlocks);
            }
        } catch (Exception e) {
            log.error("获取正文解析结果异常, link={}, userId={}", article.getLink(), userId, e);
            List<Block> tipBlocks = BlockUtils.buildErrorBlocks(article.getLink(), "正文解析失败，兜底预留网页书签");
            article.setBlocks(tipBlocks);
        }
    }

    /**
     * 处理图片
     *
     * @param result 解析结果
     * @param userId 用户ID
     */
    private void processImages(ResolveRS result, String userId) {
        if (result == null) {
            return;
        }

        // 处理封面图片
        if (StringUtils.isNotBlank(result.getCover())) {
            try {
                String coverUrl = imageProcessService.processImage(result.getCover(), userId);
                if (StringUtils.isNotBlank(coverUrl)) {
                    result.setCover(coverUrl);
                }
            } catch (Exception e) {
                log.warn("封面图片处理失败, cover={}, userId={}", result.getCover(), userId, e);
            }
        }
        // 提取图片URL
        Set<String> imgList = ImageUploadHelper.extractImgList(result);
        // 如果图片链接来自 github.io ，则不进行转存
        imgList.removeIf(img -> img.contains("github.io"));
        // 转存图片
        Map<String, String> urlMap = imageProcessService.processImages(result.getLink(), imgList, userId);
        // 替换图片块
        ImageUploadHelper.replaceImageBlock(result, result.getBlocks(), urlMap);
    }

    /**
     * 构建消息的Notion页面
     * 
     * @param messageFieldDTO 消息字段配置
     * @param content         消息内容
     * @param tags            标签数组
     * @return Notion页面对象
     */
    private Page buildMessageNotionPage(MessageFieldDTO messageFieldDTO, String content, String[] tags) {
        List<Block> blocks = Lists.newArrayList();
        // TODO 这里一个 block 不一定够用
        Block paragraph = ParagraphBlockBuilder.singleParagraphWithSingleDefaultRichText(content);
        blocks.add(paragraph);

        return Page.builder()
                .parent(Parent.build(messageFieldDTO.getDatabaseId()))
                .properties(MessageBuildHelper.propertiesBuild(messageFieldDTO, content, MsgTypeEnum.TEXT, tags))
                .children(blocks)
                .build();
    }

    /**
     * 保存消息到Notion
     * 
     * @param messageFieldDTO 消息字段配置
     * @param page            Notion页面
     * @param userId          用户ID
     * @return 是否保存成功
     */
    private boolean saveMessageToNotion(MessageFieldDTO messageFieldDTO, Page page, String userId) {
        try {
            notionClient.createPageWithRetry(messageFieldDTO.getAccessToken(), page, userId);
            return true;
        } catch (InterruptedException e) {
            log.error("保存消息到Notion失败, content={}, userId={}",
                    page.getChildren().getFirst().getParagraph().getRich_text().getFirst().getText().getContent(),
                    userId, e);
            return false;
        }
    }

    /**
     * 保存消息到Markdown
     * 
     * @param content    消息内容
     * @param tags       标签数组
     * @param userId     用户ID
     * @param userConfig 用户配置
     * @return Markdown保存结果
     */
    private boolean saveMessageToMarkdown(String content, String[] tags, String userId,
            UserConfig userConfig) {
        String title = content.substring(0, Math.min(content.length(), 100));
        ArrayList<Block> blocks = Lists
                .newArrayList(ParagraphBlockBuilder.singleParagraphWithSingleDefaultRichText(content));
        try {
            // 创建一个简单的ResolveFormRQ对象来包含消息内容
            ResolveFormRQ messageForm = new ResolveFormRQ();
            messageForm.setTitle(title);
            messageForm.setTags(Arrays.asList(tags));
            messageForm.setCategory("text");
            messageForm.setBlocks(blocks);

            // 构建Markdown保存请求
            MarkdownSaveRQ markdownRequest = MarkdownSaveRQ.builder()
                    .unionId(userId)
                    .userForm(messageForm)
                    .savePath(userConfig.getMarkdownSavePath())
                    .createAssetsDir(false) // 消息不需要资源目录
                    .downloadImages(false) // 消息不需要下载图片
                    .includeMetadata(true) // 包含元数据
                    .obConfig(userConfig.getObConfig())
                    .isArticle(false)
                    .build();

            // 保存到Markdown，使用现有的saveArticle方法
            Result<MarkdownSaveRS> markdownResult = markdownOutputService.saveArticle(markdownRequest);
            if (markdownResult.isSuccess()) {
                String filePath = markdownResult.getData().getFilePath();
                log.info("消息已保存为Markdown文件, title={}, userId={}, filePath={}",
                        title, userId, filePath);
                return true;
            } else {
                log.error("保存消息到Markdown失败, title={}, userId={}, error={}",
                        title, userId, markdownResult.getMsg());
                return false;
            }
        } catch (Exception e) {
            log.error("保存消息到Markdown异常, title={}, userId={}", title, userId, e);
            return false;
        }
    }
}
