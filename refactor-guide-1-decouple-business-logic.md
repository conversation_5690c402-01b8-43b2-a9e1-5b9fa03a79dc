# 重构指南 1：解耦业务逻辑与基础设施

## 重构原因分析

在当前项目中，业务逻辑与基础设施代码存在高度耦合的问题。以cloud-function模块为例，`S3CloudFunctionApiImpl`类直接依赖于具体的阿里云函数计算客户端实现，同时包含硬编码的逻辑判断（如地区判断）和具体的实现细节。这种设计导致了以下问题：

1. **可测试性差**：难以对业务逻辑进行单元测试，因为必须依赖真实的云服务
2. **可维护性差**：当需要更换服务提供商或修改实现时，需要修改大量代码
3. **扩展性受限**：新增其他类型的云函数支持时，需要修改现有代码逻辑
4. **违反单一职责原则**：业务逻辑和基础设施配置混合在一起

## 具体示例

在`S3CloudFunctionApiImpl.java`中，我们可以看到如下代码：

```java
@Override
public S3CloudRS transformPicture(String unionId, S3CloudRQ request) {
    // ... 业务逻辑
    if (request.getS3Config().getProvider().contains("r2")) {
        // 使用东京函数
        result = cloudFunctionDJ.invokeFunctionWithOptions(functionName, invokeFunctionRequest, headers, runtimeOptions);
    } else {
        if (SZ_SHENZHEN.equals(domestic)) {
            // 使用深圳函数
            result = cloudFunctionSZ.invokeFunctionWithOptions(functionName, invokeFunctionRequest, headers, runtimeOptions);
        } else {
            // 使用杭州函数
            result = cloudFunctionHZ.invokeFunctionWithOptions(functionName, invokeFunctionRequest, headers, runtimeOptions);
        }
    }
    // ... 其他业务逻辑
}
```

这段代码直接与阿里云函数计算服务耦合，且包含硬编码的逻辑判断。

## 完整重构方案

### 第一步：定义接口抽象

创建通用的服务接口：
```java
// 在 road-common 模块中创建
public interface CloudFunctionService {
    CloudFunctionResponse invokeFunction(CloudFunctionRequest request) throws Exception;
}
```

### 第二步：实现具体服务

为不同的云服务商创建具体实现：
```java
// 在 cloud-function 模块中创建
@Component
public class AliyunCloudFunctionService implements CloudFunctionService {
    
    private final Client client;
    
    public AliyunCloudFunctionService(Client client) {
        this.client = client;
    }
    
    @Override
    public CloudFunctionResponse invokeFunction(CloudFunctionRequest request) throws Exception {
        // 实现阿里云函数调用逻辑
        // ...
    }
}
```

### 第三步：引入工厂模式

创建服务工厂来选择合适的实现：
```java
@Component
public class CloudFunctionServiceFactory {
    
    @Autowired
    private Map<String, CloudFunctionService> services;
    
    public CloudFunctionService getService(String provider) {
        return services.get(provider);
    }
}
```

### 第四步：重构业务逻辑

修改`S3CloudFunctionApiImpl`以使用新架构：
```java
@Service
public class S3CloudFunctionApiImpl implements S3CloudFunctionApi {
    
    @Autowired
    private CloudFunctionServiceFactory serviceFactory;
    
    @Override
    public S3CloudRS transformPicture(String unionId, S3CloudRQ request) {
        // 业务逻辑与基础设施分离
        CloudFunctionService service = serviceFactory.getService(request.getS3Config().getProvider());
        CloudFunctionResponse response = service.invokeFunction(convertToCloudFunctionRequest(request));
        
        // 处理响应
        return convertToS3CloudRS(response);
    }
}
```

### 第五步：配置管理

通过配置文件管理服务提供者映射：
```yaml
# application.yml
cloud-function:
  providers:
    aliyun: com.maixi.road.cloudfunc.AliyunCloudFunctionService
    aws: com.maixi.road.cloudfunc.AwsCloudFunctionService
```

## 预期收益

1. **提高可测试性**：可以轻松创建Mock服务进行单元测试
2. **增强可维护性**：新增服务提供商只需实现接口即可
3. **提升扩展性**：支持多种云服务商而无需修改现有代码
4. **符合设计原则**：遵循依赖倒置原则和开闭原则