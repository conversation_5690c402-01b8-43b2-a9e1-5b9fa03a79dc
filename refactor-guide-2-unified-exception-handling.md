# 重构指南 2：建立统一异常处理机制

## 重构原因分析

当前项目中异常处理机制不统一，缺乏全局异常处理能力。在`S3CloudFunctionApiImpl`类中，我们看到异常处理是分散的：

```java
} catch (Exception error) {
    log.error("图片转存触发异常,message={}", error.getMessage());
} finally {
    // ... 清理工作
}
```

这种处理方式存在以下问题：

1. **异常信息丢失**：捕获所有异常但没有适当的包装和传递
2. **缺乏统一响应格式**：不同地方返回的错误信息格式不一致
3. **业务异常与系统异常混淆**：没有区分业务逻辑异常和系统运行时异常
4. **调试困难**：缺少详细的异常堆栈信息和上下文

## 具体示例

在`S3CloudFunctionApiImpl`中，异常处理只是简单记录日志然后返回空结果：
```java
} catch (Exception error) {
    log.error("图片转存触发异常,message={}", error.getMessage());
} finally {
    // ... 
}
return new S3CloudRS(new HashMap<>());
```

## 完整重构方案

### 第一步：创建统一的业务异常基类

```java
// 在 road-common 模块中创建
public class BusinessException extends RuntimeException {
    private final int code;
    private final String message;
    
    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    public BusinessException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
    
    // getter 方法
    public int getCode() { return code; }
    public String getMessage() { return message; }
}
```

### 第二步：创建系统异常类

```java
public class SystemException extends RuntimeException {
    private final int code;
    
    public SystemException(int code, String message) {
        super(message);
        this.code = code;
    }
    
    public SystemException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }
    
    public int getCode() { return code; }
}
```

### 第三步：创建统一响应类

```java
// 在 road-common 模块中创建
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseResponse<T> {
    private int code;
    private String message;
    private T data;
    
    public static <T> BaseResponse<T> success(T data) {
        return new BaseResponse<>(200, "success", data);
    }
    
    public static <T> BaseResponse<T> error(int code, String message) {
        return new BaseResponse<>(code, message, null);
    }
}
```

### 第四步：创建全局异常处理器

```java
// 在 roadserver-app 模块中创建
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public BaseResponse<?> handleBusinessException(BusinessException e) {
        log.warn("Business exception occurred: {}", e.getMessage(), e);
        return BaseResponse.error(e.getCode(), e.getMessage());
    }
    
    @ExceptionHandler(SystemException.class)
    public BaseResponse<?> handleSystemException(SystemException e) {
        log.error("System exception occurred: {}", e.getMessage(), e);
        return BaseResponse.error(e.getCode(), "系统内部错误");
    }
    
    @ExceptionHandler(Exception.class)
    public BaseResponse<?> handleException(Exception e) {
        log.error("Unexpected exception occurred: ", e);
        return BaseResponse.error(500, "未知错误");
    }
}
```

### 第五步：重构业务逻辑中的异常处理

修改`S3CloudFunctionApiImpl`：
```java
@Service
public class S3CloudFunctionApiImpl implements S3CloudFunctionApi {
    
    @Override
    public S3CloudRS transformPicture(String unionId, S3CloudRQ request) {
        try {
            // ... 原有业务逻辑
            if (StringUtils.isBlank(responseBody)) {
                throw new BusinessException(1001, "图片转存结果为空");
            }
            // ... 其他业务逻辑
        } catch (Exception e) {
            log.error("图片转存失败, unionId={}, request={}", unionId, request, e);
            throw new SystemException(500, "图片处理服务异常", e);
        }
    }
}
```

### 第六步：创建异常码枚举

```java
// 在 road-common 模块中创建
public enum ErrorCode {
    SUCCESS(200, "成功"),
    INTERNAL_ERROR(500, "系统内部错误"),
    INVALID_PARAMETER(400, "参数错误"),
    BUSINESS_ERROR(1000, "业务错误"),
    TRANSFORM_PICTURE_FAILED(1001, "图片转存失败");
    
    private final int code;
    private final String message;
    
    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public int getCode() { return code; }
    public String getMessage() { return message; }
}
```

## 预期收益

1. **统一错误响应格式**：所有API错误响应格式一致
2. **更好的异常追踪**：提供详细的异常信息和上下文
3. **易于维护**：异常处理逻辑集中管理
4. **用户体验改善**：前端可以统一处理各种错误情况
5. **调试效率提升**：异常日志更加详细和结构化