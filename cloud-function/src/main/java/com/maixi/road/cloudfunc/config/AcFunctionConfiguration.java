package com.maixi.road.cloudfunc.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.aliyun.fc20230330.Client;
import com.aliyun.teaopenapi.models.Config;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
public class AcFunctionConfiguration {

    @Value("${aliyun.fc.access-key-id}")
    private String accessKey;

    @Value("${aliyun.fc.access-key-secret}")
    private String accessSecret;

    @Value("${aliyun.fc.connect-timeout}")
    private Integer connectTimeout;

    @Value("${aliyun.fc.read-timeout}")
    private Integer readTimeout;

    @Value("${aliyun.fc.endpoint-hz}")
    private String endpointHz;

    @Value("${aliyun.fc.endpoint-sz}")
    private String endpointSz;

    @Value("${aliyun.fc.endpoint-dj}")
    private String endpointDj;

    @Bean(name = "cloudFunctionHZ")
    Client cloudFunctionHZ() throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKey)
                .setAccessKeySecret(accessSecret)
                .setEndpoint(endpointHz)
                .setConnectTimeout(connectTimeout)
                .setReadTimeout(readTimeout);
        return new Client(config);
    }

    @Bean(name = "cloudFunctionSZ")
    Client cloudFunctionSZ() throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKey)
                .setAccessKeySecret(accessSecret)
                .setEndpoint(endpointSz)
                .setConnectTimeout(connectTimeout)
                .setReadTimeout(readTimeout);
        return new Client(config);
    }

    @Bean(name = "cloudFunctionDJ")
    Client cloudFunctionDJ() throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKey)
                .setAccessKeySecret(accessSecret)
                .setEndpoint(endpointDj)
                .setConnectTimeout(connectTimeout)
                .setReadTimeout(readTimeout);
        return new Client(config);
    }
}
