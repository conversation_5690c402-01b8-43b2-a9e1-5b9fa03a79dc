package com.maixi.road.cloudfunc.puppeteer;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.fc20230330.Client;
import com.aliyun.fc20230330.models.InvokeFunctionHeaders;
import com.aliyun.fc20230330.models.InvokeFunctionRequest;
import com.aliyun.fc20230330.models.InvokeFunctionResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import com.esotericsoftware.minlog.Log;

import cn.hutool.core.io.IoUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class HeadlessService {

    @Resource
    private Client cloudFunctionHZ;

    public String fetchHtml(String url) {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("html-renderer");
        try {
            JSONObject request = new JSONObject();
            request.put("url", url);
            InvokeFunctionRequest invokeFunctionRequest = new InvokeFunctionRequest();
            invokeFunctionRequest.setQualifier("prod");
            invokeFunctionRequest.setBody(new ByteArrayInputStream(JSON.toJSONBytes(request)));
            InvokeFunctionHeaders headers = new InvokeFunctionHeaders();
            headers.setXFcInvocationType("Sync");
            RuntimeOptions runtimeOptions = new RuntimeOptions();
            InvokeFunctionResponse result;
            result = cloudFunctionHZ.invokeFunctionWithOptions("html-renderer",
                    invokeFunctionRequest,
                    headers,
                    runtimeOptions);
            String responseBody = IoUtil.read(new InputStreamReader(result.getBody(), StandardCharsets.UTF_8));
            if (StringUtils.isBlank(responseBody)) {
                Log.info(responseBody);
                return null;
            }
            return responseBody;
        } catch (Exception error) {
            log.error("图片转存触发异常,message={}", error.getMessage());
        } finally {
            stopWatch.stop();
            log.info("[{}] rt:{}", stopWatch.lastTaskInfo().getTaskName(), stopWatch.getTotalTimeSeconds());
        }
        return null;
    }
}
